
#  ![<PERSON><PERSON><PERSON>](logo.svg)  DRUPAL Theme Base
Drupal Custom Base Theme for Wingo sites
### Tooling
**Vue.js**, **Tailwind.css** & **Laravel Mix**

### Generate a subtheme from the Theme Base starterkit
First require the Theme Base:
`$ composer require wimc/theme_base 1.0.0`

Then generate your subtheme with Theme Base as a starterkit:
`$ php core/scripts/drupal generate-theme MYSUBTHEME --path themes/custom --starterkit theme_base`

Once you are done, remove `.gitkeep` files that are only here to show the expected directories organisation.
### Config
After you have generated the subtheme, you'll need to (re)set two pieces of information.

In `tailwind.config.js` replace `content: ['./base_theme.theme']` by `content: ['./mysubtheme.theme']` keeping all other entries under _content_ intact

In `mysubtheme.info.yml` set `starterkit` key to `false`
### NPM scripts
`$ npm run build` build for production envs (files are minified)

`$ npm run dev` build for local env (files are not minified)

`$ npm run watch` watches for changes (hot reload with BrowserSync)

