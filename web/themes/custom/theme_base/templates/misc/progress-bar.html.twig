{#
/**
 * @file
 * Theme override for a progress bar.
 *
 * Note that the core Batch API uses this only for non-JavaScript batch jobs.
 *
 * Available variables:
 * - label: The label of the working task.
 * - percent: The percentage of the progress.
 * - message: A string containing information to be displayed.
 */
#}
{{ attach_library('theme_base/progress') }}
<div class="progress" data-drupal-progress>
  {% if label %}
    <div class="progress__label">{{ label }}</div>
  {% endif %}
  <div class="progress__track"><div class="progress__bar" style="width: {{ percent }}%"></div></div>
  <div class="progress__percentage">{{ percent }}%</div>
  <div class="progress__description">{{ message }}</div>
</div>
