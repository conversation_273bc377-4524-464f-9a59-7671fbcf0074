!((document, Drupal) => {
  'use strict';
  Drupal.behaviors.consentBarHelper = {
    consentBar: null,
    attach: (context) => {
      setTimeout(() => {
        this.consentBar = context.querySelector('[data-gtm-consent-modal]');
        if (this.consentBar) {
          Drupal.behaviors.consentBarHelper.onClassChange(this.consentBar, () => {
          });
        }
      }, 0);
    },
    onClassChange: (node, callback) => {
      let lastClassString = node.classList.toString();
      const mutationObserver = new MutationObserver((mutationList) => {
        for (const item of mutationList) {
          if (item.attributeName === 'class') {
            const classString = node.classList.toString();
            if (classString !== lastClassString) {
              callback(mutationObserver);
              lastClassString = classString;
              break;
            }
          }
        }
      });
      mutationObserver.observe(node, { attributes: true });
      return mutationObserver;
    },
  };
})(document, Drupal);
