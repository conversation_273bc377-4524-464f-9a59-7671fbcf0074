!((document, Drupal) => {
  'use strict';
  let _ = {
    windowSize: '',
    body: null,
    menu: null,
    menuNextSibling: null,
    menuContainer: null,
    menuTogglers: null,
    menuWrapper: null,
    floatingNavigation: null,
  };
  const setWindowSize = (windowSize) => {
    if (_.windowSize !== windowSize) {
      if (windowSize === 'sm' || windowSize === 'md') {
        createMobileMenuContainer();
      }
      else {
        removeMobileMenuContainer();
        resetFloatingNavigation();
      }
    }
    _.windowSize = windowSize;
  };
  const createMobileMenuContainer = () => {
    if (!_.menuWrapper) {

      _.menuWrapper = document.createElement('div');
      _.menuWrapper.classList.add('fixed', 'flex', 'w-full', 'z-40', 'right-0', 'left-0', 'bottom-0', 'h-screen', 'max-h-full', 'transition', 'timing-1000', 'bg-primary-white', 'overflow-y-auto');
      _.menuWrapper.style.transition = 'top 0.4s cubic-bezier(1, 0, 0, 1) 0.1s';

      const menuContainerFlexbox = document.createElement('div');
      menuContainerFlexbox.classList.add('w-full', 'relative', 'flex', 'flex-col');
      menuContainerFlexbox.appendChild(_.menu);

      _.menuWrapper.style.top = '100vh';
      _.menuWrapper.appendChild(menuContainerFlexbox);
      _.body.appendChild(_.menuWrapper);
    }
  };
  const removeMobileMenuContainer = () => {
    _.menuContainer.insertBefore(_.menu, _.menuNextSibling);
    if (_.menuWrapper) {
      _.menuWrapper.remove();
      _.menuWrapper = null;
    }
  };
  const resetFloatingNavigation = () => {
    if (_.floatingNavigation.classList.contains('open')) {
      _.menuTogglers[0].click();
    }
  };
  const clickEvent = (e) => {
    const floatingNavigation = _.floatingNavigation.firstElementChild;
    floatingNavigation.classList.toggle('open');
    floatingNavigation.classList.toggle('translate-y-6');
    floatingNavigation.classList.toggle('max-w-[240px]');
    floatingNavigation.classList.toggle('max-w-full');

    const togglersGroup = e.target.parentNode;
    togglersGroup.firstElementChild.classList.toggle('hidden');
    togglersGroup.lastElementChild.classList.toggle('hidden');

    _.body.classList.toggle('fixed');
    _.body.classList.toggle('overflow-y-clip');

    if (_.body.classList.contains('fixed')) {
      _.menuWrapper.style.top = '0px';
    }
    else {
      _.menuWrapper.style.top = '100vh';
    }
  };

  window.addEventListener('DOMContentLoaded', (event) => {
    _.body = document.querySelector('body');
    _.floatingNavigation = document.querySelector('[data-floating-navigation]');
    _.menu = document.querySelector('[data-menu]');
    _.menuNextSibling = _.menu.nextSibling;
    _.menuContainer = document.querySelector('[data-menu-container]');
    _.menuTogglers = document.querySelectorAll('[data-menu-toggler]');
    if (_.menuTogglers) {
      _.menuTogglers.forEach((menuToggler) => {
        menuToggler.addEventListener('click', clickEvent);
      });
    }
    Drupal.behaviors.pubsub.subscribe('setWindowSize', setWindowSize);
    window.dispatchEvent(new Event('resize'));
  });

})(document, Drupal);
