!((document, Drupal) => {
  'use strict'
  Drupal.behaviors.video = {
    players: [],
    videoPlayers: [],
    videoPlayerDefaultOptions: {
      muted: false,
      autoplay: false,
      controls: true,
      loop: false,
      fluid: true,
      background: false
    },
    attach: (context) => {
      this.players = [...context.querySelectorAll('[data-video-player]')];
      if (this.players.length) {
        this.players.forEach(player => Drupal.behaviors.video.setVideoPlayer(player.id))
      }
    },
    setVideoPlayer: (playerId) => {
    let options = Drupal.behaviors.video.videoPlayerDefaultOptions;
    const player = videojs(playerId, options);
    Drupal.behaviors.video.videoPlayers.push({ id: playerId, player });
    }
  }
})(document, Drupal);
