((document, Drupal) => {
  'use strict';

  Drupal.behaviors.productConfiguratorFilters = {
    containers: null,
    isDarkBg: false,
    attach(context) {
      const filtersContainer = context.querySelectorAll('[data-filter-uuid]');
      this.containers = context.querySelectorAll('[data-filter-id]');
      // Subscribe to the 'filteredTab' event from pill-tab.js
      Drupal.behaviors.pubsub.subscribe('filteredTab', this.onTabFiltered.bind(this));
      if (filtersContainer.length) {
        filtersContainer.forEach(filterContainer => {
          const uuid = filterContainer.dataset.filterUuid;
          this.isDarkBg = filterContainer.dataset?.darkBg;
          const filterTab = context.querySelector(`[data-tab-id="${uuid}"]`);
          // apply default 'all' filter
          if (!location.hash) {
            this.filterTabDeselectDefault(filterTab);
          } else {
            const parameterFilter = location.hash.replace("#", "").split(/[+,]/);
            this.filterItem([parameterFilter[0]]);
          }
        });
      }
    },

    /**
     * overwrite first tab aria-selected
     * @return {void}
     * @param tab
     */
    filterTabDeselectDefault(tab) {
      const selectedTab = tab.querySelector('button[aria-selected="true"]');
      if (selectedTab) {
        this.setAnimationForTab(selectedTab, false);
      }
    },

    /**
     * change animation on selected tab.
     * @return {void}
     * @param currentTab
     * @param animated
     */
    setAnimationForTab(currentTab, animated) {
      const imgTag = currentTab.querySelector('img');
      if (!imgTag) return;
      currentTab.classList.toggle('border-primary-red', animated);
      currentTab.classList.toggle('border-grey-medium', !animated);
      imgTag.classList.toggle('scale-200', animated);
      imgTag.classList.toggle('-translate-y-6', animated);
      // check if bg is dark to add corresponding bg and text for filter
      if(this.isDarkBg) {
        currentTab.classList.toggle('bg-primary-white', animated);
        currentTab.classList.toggle('text-primary-white', !animated);
      }
    },

    /**
     * method is used to filter items based on url hash. if there is no hash it
     * should remove filter and show all items.
     * @return {void}
     * @param tab
     */
    onTabFiltered(tab) {
      const filter = tab.dataset?.urlTracking;
      const currentUrl = new URL(window.location.href);
      const currentHash = currentUrl.hash.slice(1);
      if (!currentHash) {
        // No hash present
        currentUrl.hash = `#${filter}`;
        history.pushState(null, null, currentUrl);
        this.setAnimationForTab(tab, true);
        this.filterItem([filter]);
      } else if (currentHash === filter) {
        // Hash matches the filter - show all items
        history.pushState(null, null, currentUrl.origin + currentUrl.pathname);
        this.showAllItems();
        this.setAnimationForTab(tab, false);
      } else {
        // Hash does not match the filter
        currentUrl.hash = `#${filter}`;
        history.pushState(null, null, currentUrl);
        this.filterItem([filter]);
      }
    },

    /**
     * simple filter method whereby it hides or shows items based on filters
     * received.
     * @return {void}
     * @param filters
     * @param filtersContainer
     */
    filterItem(filters) {
      Drupal.behaviors.pubsub.publish('filteredTabProductConfigurator', filters);
      let filterExists = false;
      if (filters.length && this.containers) {
        filters.forEach(filter => {
          if(this.isFilterValid(this.containers,filter)){
            filterExists = true;
          }
        });
        if(!filterExists){
          return;
        }
        this.containers.forEach(container => {
          if (container.dataset.filterId) {
            const filterIds = JSON.parse(container.dataset.filterId);
            const isVisible = filters.every(f => filterIds.includes(f));
            container.classList.toggle('hidden', !isVisible);
          }
        });
      }
    },

    /**
     * remove all filters on items and show every item.
     * @return {void}
     */
    showAllItems() {
      this.containers.forEach(container => {
        container.classList.remove('hidden');
      });
    },
    /**
     * Check if paramater is present in filters
     * @return {boolean}
     */
    isFilterValid: (containers, filter) => {
      let filterExists = false;
      containers.forEach(container => {
        if (container.dataset.filterId) {
          const filterIds = JSON.parse(container.dataset.filterId);
          if (filterIds.includes(filter)) {
            filterExists = true;
            return true;
          }
        }
      });
      return filterExists;
    }
  };
})(document, Drupal);
