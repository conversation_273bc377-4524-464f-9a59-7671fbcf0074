import AriaTablist from 'aria-tablist';

!((document, Drupal) => {
  'use strict';
  Drupal.behaviors.gallerySmartphoneTab = {
    container: null,
    panels: null,
    tabs: null,
    tab: null,
    tabList: null,
    activeTabIndex: 0,
    timer: null,
    observer: null,
    isPaused: false,
    options: {
      root: null,
      rootMargin: '0px',
      threshold: 0.5,
    },
    attach: function attach(context) {
      this.container = context.querySelector('[data-gallery-container]');
      this.panels = [...this.container.querySelectorAll('[data-smartphone-panel]')];
      this.tabs = [...this.container.querySelectorAll('[data-smartphone-tab]')];
      this.tab = this.container.querySelector('[data-gallery-tab]');
      if (this.tab) {
        this.tab.addEventListener('mouseover', () => {
          Drupal.behaviors.gallerySmartphoneTab.isPaused = true;
        });
        this.tab.addEventListener('mouseout', () => {
          Drupal.behaviors.gallerySmartphoneTab.isPaused = false;
        });
      }
      if (this.container) {
        this.observer = new IntersectionObserver(Drupal.behaviors.gallerySmartphoneTab.togglePause, this.options);
        this.observer.observe(this.container);
        this.tabList = new AriaTablist(this.tab, {
          focusableTabs: true,
          onReady: () => {
            this.panels.forEach((panel, index) => {
              index === 0 ? Drupal.behaviors.gallerySmartphoneTab.showPanel(panel) : Drupal.behaviors.gallerySmartphoneTab.hidePanel(panel);
            });
            this.tabs.forEach((tab, index) => {
              index === 0 ? Drupal.behaviors.gallerySmartphoneTab.highlightTab(tab) : Drupal.behaviors.gallerySmartphoneTab.dimTab(tab);
            });
          },
          onOpen: (panel, tab) => {
            // Reset interval and tab index
            clearInterval(this.timer);
            Drupal.behaviors.gallerySmartphoneTab.setTimer();

            this.activeTabIndex = this.panels.findIndex(panelElement => panelElement === panel);
            this.panels.forEach(panelElement => {
              panelElement === panel ? Drupal.behaviors.gallerySmartphoneTab.showPanel(panelElement) : Drupal.behaviors.gallerySmartphoneTab.hidePanel(panelElement);
            });
            this.tabs.forEach(tabElement => {
              tabElement === tab ? Drupal.behaviors.gallerySmartphoneTab.highlightTab(tabElement) : Drupal.behaviors.gallerySmartphoneTab.dimTab(tabElement);
            });
          },
        });
        if (this.tabList) {
          Drupal.behaviors.gallerySmartphoneTab.setTimer();
        }
      }
    },
    setTimer: () => {
      Drupal.behaviors.gallerySmartphoneTab.timer = setInterval(() => {
        if (!Drupal.behaviors.gallerySmartphoneTab.isPaused) {
          Drupal.behaviors.gallerySmartphoneTab.activeTabIndex = (Drupal.behaviors.gallerySmartphoneTab.activeTabIndex + 1) % Drupal.behaviors.gallerySmartphoneTab.tabs.length;
          Drupal.behaviors.gallerySmartphoneTab.tabList.open(Drupal.behaviors.gallerySmartphoneTab.activeTabIndex);
        }
      }, 4000);
    },
    togglePause: (entries) => {
      entries.forEach(entry => {
        Drupal.behaviors.gallerySmartphoneTab.isPaused = !entry.isIntersecting;
      });
    },
    showPanel: (panel) => {
      setTimeout(() => {
        panel.classList.remove('opacity-0', 'translate-y-12');
        panel.classList.add('opacity-100', 'translate-y-0');
      }, 150);
    },
    hidePanel: (panel) => {
      panel.classList.remove('opacity-100', 'translate-y-0');
      panel.classList.add('opacity-0', 'translate-y-12');
    },
    highlightTab: (tab) => {
      tab.classList.add('border-primary-red', 'border-2');
    },
    dimTab: (tab) => {
      tab.classList.remove('border-primary-red', 'border-2');
    },
  };
})(document, Drupal);
