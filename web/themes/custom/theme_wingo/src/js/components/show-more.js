((document, Drupal) => {
  'use strict';

  Drupal.behaviors.showMore = {
    attach: (context) => {
      const showMoreFullText = context.querySelectorAll('[data-show-more-container]');

      showMoreFullText.forEach(showMore => {
        const uuid = showMore.dataset?.showMoreContainer;
        if (uuid) {
          const showMoreButton = context.querySelector(`[data-show-more-button="${uuid}"]`);
          if (showMoreButton) {
            const shortText = showMoreButton?.previousElementSibling;
            Drupal.behaviors.showMore.styleParagraph(shortText);

            const fullTextContainer = showMoreButton.nextElementSibling;

            showMoreButton.addEventListener('click', () => {
              Drupal.behaviors.showMore.handleShowMoreEvent(showMoreButton, fullTextContainer);
            });
          }
        }
      });
    },

    styleParagraph: (element) => {
      element.classList.add('inline');
    },

    handleShowMoreEvent: (showMoreButton, element) => {
      showMoreButton.classList.add('hidden');
      element.classList.remove('hidden');
      Drupal.behaviors.showMore.styleParagraph(element.firstElementChild);
    }
  };
})(document, Drupal);
