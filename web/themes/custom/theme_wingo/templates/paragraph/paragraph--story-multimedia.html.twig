{% extends "paragraph.html.twig" %}
{% block content %}
	{{ attach_library('theme_wingo/story-multimedia') }}
	{% import '@theme_wingo/_macros/videos/video.twig' as video %}
	{% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
	{% import '@theme_wingo/_macros/pictures/picture.twig' as picture %}
	{% set scrollable = formatted_data.allow_scrolling ? 'scrollable' : 'fixed' %}
	{% set classes = [
		'transition-all',
		'duration-300'
	] %}
	<div{{ attributes.addClass(classes) }}
				class="duration-500 ease-in-out px-6 sm:px-24" data-stories-multimedia="{{ scrollable }}">
		<div class="flex relative h-screen overflow-hidden rounded-3xl duration-500 ease-in-out transition-[border] scene"
				 data-story-multimedia-scene>
			{% for section in formatted_data.content %}
        {% set zIndex = 100 - loop.index %}
        <div class="absolute inset-0 flex flex-col justify-end {%- if not loop.first %} opacity-0 {%- endif %}"
             style="z-index: {{ zIndex }};"
             data-story-multimedia-sections>
          <div class="flex flex-col-reverse sm:flex-row px-6 sm:px-12 lg:px-24 h-full sm:!pr-0">
            <div class="w-full sm:w-[50vw] flex flex-none transition-all opacity-0 translate-y-6 duration-500"
                 data-story-multimedia-text>
              <div
                class="{% if scrollable == 'scrollable' -%} self-center text-primary-black {% else -%} self-end text-primary-white {%- endif %} pb-28 sm:pb-24">
                <h2
                  class="text-16 sm:text-40 font-radikal-bold mb-4 pt-6 sm:pt-0 sm:pr-40 sm:[&>p]:pr-2">{{ section.text | raw }}</h2>
                {% if section.cta.path %}
                  {{ trigger.data({variant: section.cta.variant, label: section.cta.title, icon: section.cta.icon, path: section.cta.path }) }}
                {% endif %}
              </div>
            </div>
            {% if section.media %}
              {% if scrollable == 'scrollable' %}
                <div class="flex h-full -mx-14 sm:mx-0 py-12">
                  <div class="rounded-4xl transition-all scale-75 my-0 duration-500 ease-in-out overflow-hidden"
                       data-story-multimedia-image>
                    {{ picture.data({media:section.media, cover:true, classes:['h-full w-full'] }) }}
                  </div>
                </div>
              {% else %}
                <div
                  class="absolute flex h-full -z-[1] {% if scrollable == 'scrollable' -%} inset-0 {%- else -%} -inset-x-24 {%- endif %}">
                  <div class="flex w-full" data-story-multimedia-image>
                    {{ picture.data({media:section.media, cover:true, classes:['h-full w-full'] }) }}
                  </div>
                </div>
              {% endif %}
						{% endif %}
					</div>
				</div>
			{% endfor %}
			{% if formatted_data.media %}
				<div class="absolute inset-0 z-0 h-full hidden sm:flex">
					{% if formatted_data.media.type == 'video' %}
						{{ video.data({id: formatted_data.uuid, videoUrl: formatted_data.media.path }) }}
					{% endif %}
					{% if formatted_data.media.type == 'image' %}
						<div data-story-multimedia-scene-image>
							{{ picture.data({media:formatted_data.media, cover:true, classes:['h-full w-full'] }) }}
						</div>
					{% endif %}
					{% if formatted_data.media.type == 'svg' %}
						<div class="self-center -ml-24 transition-all duration-500 rotate-0 translate-y-1/2 ease-in-out"
								 data-story-multimedia-scene-image-scroll>
							<img src="{{ formatted_data.media.path }}" alt="{{ formatted_data.media.alt_text }}"/>
						</div>
					{% endif %}
				</div>
			{% endif %}
		</div>
	</div>
	<style>
      .video-js {
          height: 100%;
          width: 100%;
      }

      .video-js .vjs-tech {
          object-fit: cover;
      }

      .pin-spacer {
          width: 100% !important;
      }

      .scene {
          width: 200% !important;
      }
	</style>
{% endblock content %}
