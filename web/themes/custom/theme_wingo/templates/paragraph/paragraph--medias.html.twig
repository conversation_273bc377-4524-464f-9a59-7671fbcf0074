{% extends "paragraph.html.twig" %}
{% block content %}
	{% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
	{% set label = 'wimc_cta_label_download'|t %}
	<div class="content-grid">
		<div class="col-start-2 py-10">
			<h1 class="font-radikal-bold text-primary-dark text-center">{{ formatted_data.title }}</h1>
			{% for media in formatted_data.medias %}
				{% if media.date is not null %}
					{% set mediaValue = [media.extension|upper, media.date ] %}
				{% else %}
					{% set mediaValue = media.extension|upper %}
				{% endif %}
				<div class="flex flex-col sm:flex-row justify-between my-10 items-start sm:items-center gap-4 sm:gap-0">
					<div>
						<div class="font-radikal-bold text-20">{{ media.name }}</div>
						<div class="text-14 py-2 text-grey-semiDark">{{ mediaValue|safe_join(' · ') }}</div>
					</div>
					{{ trigger.data({ variant: "tertiary", disabled: false, label: label, target: "_blank", path: media.path, icon: media.type }) }}
				</div>
			{% endfor %}
		</div>
	</div>
{% endblock content %}
