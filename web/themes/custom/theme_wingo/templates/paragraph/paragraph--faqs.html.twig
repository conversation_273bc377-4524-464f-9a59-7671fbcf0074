{#
/**
 * @file
 * Default theme implementation to display a paragraph.
 *
 * Available variables:
 * - paragraph: Full paragraph entity.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - paragraph.getCreatedTime() will return the paragraph creation timestamp.
 *   - paragraph.id(): The paragraph ID.
 *   - paragraph.bundle(): The type of the paragraph, for example, "image" or "text".
 *   - paragraph.getOwnerId(): The user ID of the paragraph author.
 *   See Drupal\paragraphs\Entity\Paragraph for a full list of public properties
 *   and methods for the paragraph object.
 * - content: All paragraph items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - paragraphs: The current template type (also known as a "theming hook").
 *   - paragraphs--type-[type]: The current paragraphs type. For example, if the paragraph is an
 *     "Image" it would result in "paragraphs--type--image". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - paragraphs--view-mode--[view_mode]: The View Mode of the paragraph; for example, a
 *     preview would result in: "paragraphs--view-mode--preview", and
 *     default: "paragraphs--view-mode--default".
 * - view_mode: View mode; for example, "preview" or "full".
 * - logged_in: Flag for authenticated user status. Will be true when the
 *   current user is a logged-in member.
 * - is_admin: Flag for admin user status. Will be true when the current user
 *   is an administrator.
 * formatted_data: formatted paragraph based on its type a fields, check theme.
 *  - Use  {{ dump(formatted_data) }} to see all the available fields
 *  - @see theme_wingo_preprocess_paragraph()
 *
 * @see template_preprocess_paragraph()
 *
 * @ingroup themeable
 */
	 not paragraph.isPublished() ? 'paragraph--unpublished',
#}
{% extends "paragraph.html.twig" %}
{% block content %}
  {{ attach_library('theme_wingo/split-pin') }}
  {{ attach_library('theme_wingo/dynamic-faqs') }}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% import '@theme_wingo/_macros/accordion/accordion.twig' as accordion %}
  {% import '@theme_wingo/_macros/pictures/picture.twig' as picture %}
  {% import '@theme_wingo/_macros/seo/schema-markup-generator.twig' as schema %}
  <div class="content-grid py-0 sm:py-16" data-faqs="{{ formatted_data.uuid }}">
    <div class="default-grid relative" data-split-container>
      {% if formatted_data.media %}
        <div class="col-span-12 sm:col-span-6 -mx-6 sm:mx-0 min-h-[300px] h-full" data-split-container-pinned-element>
          <div class="flex relative h-full">
            <div class="absolute bottom-4 sm:bottom-0 p-6 max-w-sm">
              <div class="text-h3 text-primary-white mb-4 sm:mb-0">{{ formatted_data.title }}</div>
            </div>
            {{ picture.data({media: formatted_data.media, cover:true, classes:['h-full w-full rounded-3xl'] }) }}
          </div>
        </div>
      {% endif %}
      <div class="col-span-12 {% if formatted_data.media %}sm:col-span-6{% endif %} flex flex-col rounded-t-3xl bg-primary-white px-6 py-6 sm:px-0 sm:pt-0 -mx-6 sm:mx-0 -mt-16 sm:mt-0 z-10" data-split-container-free-element>
        {% if formatted_data.media is not defined %}
          <h2 class="flex justify-center text-center text-h3 mb-4 sm:mb-6">{{ formatted_data.title }}</h2>
        {% endif %}
        {% if formatted_data.content.filters is defined and formatted_data.content.filters|length > 1 %}
          <div class="flex justify-center pb-6">
            {% set filters = [] %}
            {% for key, filter in formatted_data.content.filters %}
              {% set filters = filters|merge([{id: formatted_data.uuid ~ loop.index, variant: 'secondary', name: filter.name, label: filter.name, value: filter.id, pillData: filter.count}]) %}
            {% endfor %}
            {% include '@theme_wingo/_components/filters/filters-group.html.twig' with { filtersGroupId: formatted_data.uuid, filters: filters, centerFilters: true } only %}
          </div>
        {% endif %}
        {% if formatted_data.enable_search_field %}
          <div class="w-full max-w-5xl mx-auto my-4 relative">
            <input data-faqs-search class="pl-[50px] w-full bg-transparent placeholder:text-slate-400 text-slate-700 text-sm border border-grey-medium rounded-md pr-3 py-3 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-300 shadow-sm focus:shadow" placeholder="{{ 'wimc_search_faq'|t }}" />
            <img class="w-5 absolute top-1/2 left-[15px] -translate-y-1/2 cursor-pointer" src="/{{ active_theme_path() }}/dist/svg/utils/magnifying-glass.svg" aria-hidden="true" alt="{{ 'wimc_search_link'|t }}" width="20" height="20">
          </div>
        {% endif %}
        <div class="mx-auto flex flex-col justify-center {% if formatted_data.media is not defined %}max-w-5xl{% else %}max-w-lg{% endif %}">
          {% if formatted_data.display_total_results %}
            {% set total_results = formatted_data.total_results %}
            <p class="font-radikal-regular text-14 w-full max-w-5xl mx-auto my-4 w-full max-w-5xl mx-auto mb-3" data-result-search-faq data-result-keyword-singular="{{ 'wimc_result_singular'|t  }}" data-result-keyword-plural="{{ 'wimc_result_plural'|t }}">{{ total_results }} {% if total_results <= 1 %}{{ 'wimc_result_singular'|t }}{% else %}{{ 'wimc_result_plural'|t }}{% endif %}</p>
          {% endif %}
          {# faqData is used to generate schema markup for JSON-LD #}
          {% set faqData  = [] %}
          {% set accordion_data = [] %}
          {% for item in formatted_data.content.items %}
            {% set accordion_data = accordion_data|merge([{title: item.question, body: item.answer, dataAttributes: { faq_item: null, filters: item.filters ? item.filters|json_encode(constant('JSON_UNESCAPED_UNICODE')) : null }}]) %}
            {% set faqData  = faqData |merge([{
              "@type": "Question",
              "name": item.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": item.answer|striptags
              }
            }]) %}
          {% endfor %}
          {{ accordion.data({items: accordion_data, border: true, heading: 'h4'}) }}
        </div>
        {% if formatted_data.cta %}
          <div class="pt-8 flex justify-center">
            {{ trigger.data({variant: 'tertiary', rtl: true, label: formatted_data.cta.title, target: formatted_data.cta.target , path: formatted_data.cta.path , icon: formatted_data.cta.icon? formatted_data.cta.icon : 'arrow-leftwards', additionalClasses : formatted_data.cta.tracking_class }) }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
  {{ schema.data({type: "FAQPage", data: faqData, key: "mainEntity" }) }}
{% endblock content %}
