{#
/**
 * Paragraphe theme implementation to display intro product.
 *
 * Available variables:
 * - paragraph:
      - It can be displayed on it's own.
      - It can be displayed in stage simple.
 *   Use the library container-width.js to animate the width of the container.
 * */
#}

{% extends "paragraph.html.twig" %}
{% block content %}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% import '@theme_wingo/_macros/prices/price.twig' as price %}
  <div class="px-8 sm:px-24 ease-out duration-150" data-container='{ "start": "top-=500px bottom", "end": "center bottom-=200px" }'>
    <div class="relative col-span-12 flex flex-col md:flex-row items-start md:items-center {% if formatted_data.title %} justify-between {% else %} justify-end {% endif %} w-full default-grid rounded-t-3xl py-8 content-grid {% if not formatted_data.bg_color %} bg-primary-white {% endif %}" {% if formatted_data.bg_color %} style="background-color: {{ formatted_data.bg_color.hex }}" {% endif %}>
      {% if formatted_data.title %}
        <div class="flex flex-row-reverse sm:flex-row items-center justify-end sm:justify-center text-20 md:text-24 font-radikal-bold">
          <h1 class="text-20 md:text-24 max-w-[70%] sm:max-w-none{% if formatted_data.bg_color and formatted_data.bg_color.hex is dark_bg %} text-primary-white{% endif %}">{{ formatted_data.title | raw }}</h1>
        </div>
      {% endif %}
      {% if formatted_data.product_variant is not empty or formatted_data.cta is not empty %}
        <div class="flex justify-between sm:justify-start sm:items-center sm:flex-row sm:w-auto {%- if not formatted_data.product_variant.number_of_installments %} items-end w-full{% endif %}">
          {% if formatted_data.product_variant.price %}
            <div class="sm:mt-6 md:mt-0 md:mb-0 {% if formatted_data.bg_color and formatted_data.bg_color.hex is dark_bg %} text-primary-white{% endif %} {%- if formatted_data.product_variant.number_of_installments %} mb-6 mr-4{%- else %} mb-0 mt-3 mr-12{% endif %}">
              {{ price.data ({ defaultPrice: formatted_data.product_variant.price.default, promo: formatted_data.product_variant.price.promo, minPrice:formatted_data.product_variant.price.min, minPriceLabel:formatted_data.product_variant.price.min_label }) }}
            </div>
          {% endif %}
          {% if formatted_data.cta %}
            <div class="flex flex-col items-center">
              {% set offert_text = formatted_data.product_variant.offer_text ? formatted_data.product_variant.offer_text : formatted_data.product_variant.product.offer_text %}
              {% if offert_text %}
                <div class="text-10 font-radikal-bold rounded-t-lg p-2 bg-secondary-yellow">
                  {{ offert_text|upper }}
                </div>
              {% endif %}
              <div>
                {{ trigger.data({variant: 'primary', disabled: false, label: formatted_data.cta.title, target: formatted_data.cta.target , path: formatted_data.cta.path , icon: (formatted_data.cta.icon ? formatted_data.cta.icon : 'arrow-diagonalwards'), additionalClasses : formatted_data.cta.tracking_class }) }}
              </div>
            </div>
          {% elseif formatted_data.product_variant.number_of_installments %}
            <p class="text-14 text-grey-dark">
              {% trans %}
                for {{ formatted_data.product_variant.number_of_installments }} months + CHF
                <br> {{ formatted_data.product_variant.upfront_fee }}.– to accomplish
              {% endtrans %}
            </p>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
{% endblock %}
