{% extends "paragraph.html.twig" %}
{% block content %}
	{{ attach_library('theme_wingo/content-swiper') }}
	{% import '@theme_wingo/_macros/section-title/section-title.twig' as section_title %}
	{% import '@theme_wingo/_macros/cards/item-teaser-card.twig' as teaser %}
	{% if formatted_data.z_title %}
		<div class="sm:mx-auto flex flex-col justify-center sm:px-20 md:px-0 pt-12 -mb-12 sm:pt-12 sm:pb-6 sm:mb-0">
			{{ section_title.data({ title: formatted_data.z_title }) }}
		</div>
	{% endif %}
	{% set swiperProperties = {
		"mobile": {
			"slidesPerView": 1.3,
			"spaceBetween": 10,
			"centeredSlides": false,
		},
		"sm": {
			"slidesPerView": 2,
			"spaceBetween": 20,
		},
		"md": {
			"slidesPerView": 3,
			"spaceBetween": 20,
		},
		"lg": {
			"slidesPerView": 4,
			"spaceBetween": 20,
		},
		uuid: formatted_data.uuid
	} %}
	<div class="content-grid mt-4">
		<div class="col-span-12 flex my-12">
			<div class="swiper" data-swiper>
				<div class="swiper-wrapper" style="width: 100vw!important"
						 data-swiper-properties="{{ swiperProperties|json_encode }}">
					{% for product_option in formatted_data.product_options %}
						<div class="swiper-slide transition-all sm:hover:!scale-[1] sm:!scale-[0.980]">
							{{ teaser.data({path: product_option.path, picture: product_option.asset, lead: product_option.lead}) }}
						</div>
					{% endfor %}
				</div>
			</div>
		</div>
		<div class="col-span-12 hidden sm:flex w-full justify-end">
			{% include '@theme_wingo/_components/content-swiper-arrows/content-swiper-arrows.html.twig' with { isNegative: true } %}
		</div>
	</div>
{% endblock content %}
