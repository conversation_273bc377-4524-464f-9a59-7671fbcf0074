{{ attach_library('theme_wingo/multiselect') }}
<div class="flex flex-col opacity-0 transition-opacity"
     data-select='{
              "selectId":"{{ selectId }}",
              "groupId":"{{ groupId }}",
              "openAbove":"{{ editSpacing|default(false) }}"
          }'
>
  {% if selects is not empty %}
    <div class="flex flex-col sm:flex-row gap-x-6 gap-y-2">
      {% for select in selects %}
        <select {% if not single -%} multiple {%- endif %} data-select-placeholder="{{ select.placeholder }}" {% if searchable %} data-searchable="{{ searchable }}" {% endif %}>
          {% for option in select.options %}
            <option
                  value="{{ option.value }}" {% if option.selected %} selected="selected" {% endif %}>{{ option.label }}</option>
          {% endfor %}
        </select>
      {% endfor %}
    </div>
  {% endif %}
  {% if not single %}
    <div class="flex flex-col sm:flex-row mt-6 items-start px-0 sm:px-6 text-18">
      <div class="flex text-primary-red cursor-pointer items-center py-2 mr-6 pointer-events-none opacity-0 transition-opacity"
           data-select-refresh>
        <span class="pr-2 font-radikal-bold select-none">{{ 'wimc_reset_filter'|t }}</span>
        <img src="/{{ active_theme_path() }}/dist/svg/utils/refresh.svg" alt="{{ 'wimc_reset_filter'|t }}"
             class="cursor-pointer self-center mr-3"/>
      </div>
      <div class="tail-move-container-{{ selectId }} flex flex-wrap"></div>
    </div>
  {% endif %}
</div>
