{% if product_variant %}
  {{ attach_library('theme_wingo/order-bar') }}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% import '@theme_wingo/_macros/prices/price.twig' as price %}
  {% import '@theme_wingo/_macros/countdown/countdown.twig' as countdown %}
  {% set run_out_message = 'wimc_runs_out'|t %}
  {% set day_message = 'wimc_day'|t %}
  {% set days_message = 'wimc_days'|t %}
  {% set countdown_attr = { id: product_variant.uuid, color: product_variant.promo_color, startTimestamp: product_variant.validity_ts.start_date, endTimestamp: product_variant.validity_ts.end_date, message: {title: run_out_message, day: day_message, days: days_message}, display: "orderBar" } %}
  <div data-animated-order-bar class="sm:px-6 sm:py-4 flex justify-center fixed bottom-0 w-full transition-all ease-in-out-expo duration-300 z-20">
    <div class="shadow-md lg:shadow-lg flex flex-row justify-between items-center bg-primary-white w-full overflow-hidden py-3 px-6 sm:px-3 rounded-2xl relative">
      <div class="sm:w-2/3 flex sm:justify-between sm:flex-row flex-col-reverse">
        <div class="flex flex-col md:items-center md:flex-row items-start sm:ml-3 sm:w-1/2">
          <div class="mr-6 md:ml-3 text-24 font-radikal-bold sm:block hidden">
            {% if product_variant.product %}
              {{ product_variant.product.name}}
            {% else %}
              {{ product_variant.name }}
            {% endif %}
          </div>
          {{ price.data({ defaultPrice: product_variant.price.default, promo: product_variant.price.promo, display: "small", strikeThroughColor: product_variant.strikethrough_color }) }}
        </div>
        <div class="flex flex-col md:flex-row justify-center items-center sm:w-1/2">
          {{ countdown.data(countdown_attr) }}
        </div>
      </div>
      <div class="sm:w-1/3 flex justify-end">
        {{ trigger.data({variant: 'primary', disabled: false, label: product_variant.cta.title, target: product_variant.cta.target, path: product_variant.cta.path, icon: product_variant.cta.icon_primary ? product_variant.cta.icon_primary : 'arrow-diagonalwards', additionalClasses: product_variant.cta.tracking_class }) }}
      </div>
    </div>
  </div>
{% endif %}
