{% if product_variant %}
  {{ attach_library('theme_wingo/order-bar-v2') }}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% import '@theme_wingo/_macros/prices/price.twig' as price %}
  {% import '@theme_wingo/_macros/countdown/countdown-v2.twig' as countdown %}
  {% set countdown_attr = {
    id: product_variant.uuid,
    color: product_variant.promo_color,
    startTimestamp: product_variant.validity_ts.start_date,
    endTimestamp: product_variant.validity_ts.end_date,
    display: "orderBar"
  } %}
  <div data-animated-order-bar class="flex flex-col justify-center fixed bottom-0 w-[89%] left-1/2 -translate-x-1/2 transition-all ease-in-out-expo duration-300 z-20 rounded-xl sm:hidden" >
    {% if product_variant.last_chance %}
      <div class="bg-grey-lightest px-6 rounded-t-lg mx-auto w-10/12 shadow-teaser-card">
        {{ countdown.data(countdown_attr) }}
      </div>
    {% endif %}
    <div class="flex flex-col w-full shadow-md lg:shadow-lg flex flex-row justify-between items-stretch bg-primary-white w-full overflow-hidden py-3 px-6 relative rounded-xl bg-primary-white shadow-teaser-card">
      <div class="flex justify-between items-baseline mb-3">
        <div class="mr-6 md:ml-3 text-16 font-radikal-bold">
            {{ product_variant.name }}
        </div>
        <div>
          {{ price.data({ defaultPrice: product_variant.price.default, promo: product_variant.price.promo, display: "medium", strikeThroughColor: product_variant.strikethrough_color }) }}
        </div>
      </div>
      <div class="mb-3">
        {% set ctaLabel = product_variant.price.promo ? product_variant.cta.title ~ " - " ~ product_variant.price.promo :  product_variant.cta.title ~ " - " ~ product_variant.price.default %}
        {{ trigger.data({variant: 'primary', disabled: false, label: ctaLabel, target: product_variant.cta.target, path: product_variant.cta.path, icon: product_variant.cta.icon_primary ? product_variant.cta.icon_primary : 'arrow-diagonalwards', width: "full",  additionalClasses: " !w-full !justify-center ecom-ev-click_add_to_cart"  }) }}
      </div>
    </div>
  </div>
{% endif %}
