{#
{{ attach_library('theme_wingo/horizontal-scroller') }}
#}
{% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}

<div class="content-grid">
  <div class="default-grid py-4">
    <div class="flex flex-col justify-center items-center bg-secondary-blue rounded-4xl col-span-4 py-4">
      <h3 class="text-h3 mb-4">Primary</h3>
      <div class="flex flex-col">
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: true, label: 'Disabled'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: true, icon: 'arrow-rightwards', label: 'Disabled w/ icon'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: false, label: 'Order', target: 'https://www.wingo.ch' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: false, label: 'Order now', target: '/', icon: 'arrow-rightwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: false, label: 'Please feel free to order now', target: '/', icon: 'arrow-downwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'primary', disabled: false, label: 'Please feel free to order right away', target: '/', icon: 'arrow-diagonalwards' }) }}
        </div>
      </div>
    </div>
    <div class="flex flex-col justify-center items-center bg-secondary-green rounded-4xl col-span-4 py-4">
      <h3 class="text-h3 mb-4">Secondary</h3>
      <div class="flex flex-col">
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: true, label: 'Disabled'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: true, icon: 'arrow-rightwards', label: 'Disabled w/ icon'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: false, label: 'Order', target: '/' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: false, label: 'Order now', target: '/', icon: 'arrow-rightwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: false, label: 'Please feel free to order now', target: '/', icon: 'arrow-downwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'secondary', disabled: false, label: 'Please feel free to order right away', target: '/', icon: 'arrow-diagonalwards' }) }}
        </div>
      </div>
    </div>
    <div class="flex flex-col justify-center items-center bg-secondary-yellow rounded-4xl col-span-4 py-4">
      <h3 class="text-h3 mb-4">Tertiary</h3>
      <div class="flex flex-col">
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: true, label: 'Disabled'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: true, icon: 'arrow-rightwards', label: 'Disabled w/ icon'}) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: false, label: 'Order', target: '/' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: false, label: 'Order now', target: '/', icon: 'arrow-rightwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: false, label: 'Please feel free to order now', target: '/', icon: 'arrow-downwards' }) }}
        </div>
        <div class="mb-2">
          {{ trigger.data({variant: 'tertiary', disabled: false, label: 'Please feel free to order right away', target: '/', icon: 'arrow-diagonalwards' }) }}
        </div>
      </div>
    </div>
  </div>
</div>

<div class="py-6">
  <div class="flex justify-center items-center w-full">
    <img
      src="https://www.wingo.ch/sites/default/files/styles/dynamic_resize_width_1920/public/images/wingo-europe-full_2.jpg"
      alt="Wingo Europe" aria-hidden="true">
  </div>
</div>

<div class="content-grid">
  <div class="default-grid py-4">
    <div class="flex flex-col justify-center items-center bg-secondary-yellow rounded-4xl col-span-4">
      <div class="text-display">Display</div>
      <h1 class="text-h1">Heading 1</h1>
      <h2 class="text-h2">Heading 2</h2>
      <h3 class="text-h3">Heading 3</h3>
      <h4 class="text-h4">Heading 4</h4>
      <h5 class="text-h5">Heading 5</h5>
      <h6 class="text-h6">Heading 6</h6>
    </div>
    <div class="flex flex-col justify-center items-center bg-secondary-blue rounded-4xl col-span-4">
      <div class="text-96 font-radikal-bold">text-96</div>
      <div class="text-80 font-radikal-bold">text-80</div>
      <div class="text-64 font-radikal-bold">text-64</div>
      <div class="text-56 font-radikal-bold">text-56</div>
      <div class="text-48 font-radikal-bold">text-48</div>
      <div class="text-40 font-radikal-bold">text-40</div>
      <div class="text-36 font-radikal-bold">text-36</div>
      <div class="text-32 font-radikal-bold">text-32</div>
      <div class="text-28 font-radikal-bold">text-28</div>
      <div class="text-24 font-radikal-bold">text-24</div>
      <div class="text-20 font-radikal-bold">text-20</div>
      <div class="text-18 font-radikal-bold">text-18</div>
      <div class="text-16 font-radikal-bold">text-16</div>
      <div class="text-14 font-radikal-bold">text-14</div>
    </div>
    <div class="flex flex-col justify-center items-center bg-secondary-green rounded-4xl col-span-4 py-6">
      <div class="text-96 font-radikal-regular">text-96</div>
      <div class="text-80 font-radikal-regular">text-80</div>
      <div class="text-64 font-radikal-regular">text-64</div>
      <div class="text-56 font-radikal-regular">text-56</div>
      <div class="text-48 font-radikal-regular">text-48</div>
      <div class="text-40 font-radikal-regular">text-40</div>
      <div class="text-36 font-radikal-regular">text-36</div>
      <div class="text-32 font-radikal-regular">text-32</div>
      <div class="text-28 font-radikal-regular">text-28</div>
      <div class="text-24 font-radikal-regular">text-24</div>
      <div class="text-20 font-radikal-regular">text-20</div>
      <div class="text-18 font-radikal-regular">text-18</div>
      <div class="text-16 font-radikal-regular">text-16</div>
      <div class="text-14 font-radikal-regular">text-14</div>
    </div>
  </div>
  {#<div class="default-grid">
    <div class="horizontal-scroller scene h-screen col-span-12 flex flex-nowrap overflow-clip rounded-4xl my-8">
      <div class="absolute flex w-full items-center" :ref="'sectionsBackground'"
           :style="{'height': '100%', 'width': `${sections.length * 80}vw`, 'background-size': 'cover', 'background-image': 'url(https://images.unsplash.com/photo-1532879311112-62b7188d28ce)'}">
      </div>
      <div v-for="section in sections" :ref="'sections'" class="flex w-full items-center">
        <div class="flex items-center ">
          <div class="flex max-w-lg">
            <div class="text-display">[[ section.title ]]</div>
          </div>
        </div>
      </div>
      <div v-for="(section, i) in sections" :ref="'sectionsImage'" class="absolute flex w-full items-center self-end"
           :style="{'width': '100vw', 'left': `${i * 90}vw`}">
        <div class="flex items-center w-screen">
          <img :src="section.image" alt="img"/>
        </div>
      </div>
    </div>
  </div>
</div>
#}
<div class="py-6">
  <div class="flex justify-center items-center w-full">
    <img
      src="https://www.wingo.ch/sites/default/files/styles/dynamic_resize_width_1920/public/images/wingo-swiss-pro%20%281%29.jpg"
      alt="Wingo Swiss Pro" aria-hidden="true">
  </div>
</div>
