{#
/**
 * Theme implementation to display a responsive table with sorting possibility.
 *
 * Parameters attributes:
 *  - attr: object with the following keys:
 *    - headers: An array of headers to display in the table. Each header is an array with the following keys: title, key, sortable, sortDirection, sortKey, sortDefault, sortDefaultDirection.
 *    - rows: An array of rows to display in the table. Each row is an array with the following keys that correspond to the headers. The index of the row array should match the index of the headers array.
 *    - isDarkBg: A boolean to determine if the content of the table should have white color.
 *    - defaultEmtpyMessage: A string to display when there are no rows to display.
 *    - enableSorting: A boolean to determine if the table should be sortable.
 *
 *  - @see js/components/datagrid.js
 */
#}
{% macro data(attr = { headers: [], rows: [], isDarkBg: false, defaultEmtpyMessage: '', enableSorting: true }) %}
  {{ attach_library('theme_wingo/datagrid') }}
  {% import '@theme_wingo/_macros/prices/price.twig' as price %}
  {% set isDarkBg = attr.isDarkBg %}
  <div class="overflow-x-auto overflow-y-hidden w-full sm:w-auto">
    <table {% if attr.enableSorting %}data-sort-table{% endif %}>
      <thead>
        <tr>
          {% for key, header in attr.headers %}
            <th role="columnheader" {% if not header.sortable %}data-sort-method="none"{% endif %}  class="relative after:border-b-grey-dark after:border-x-transparent !text-16 !px-4 {% if not header.sortable and isDarkBg %} no-sort !text-primary-white{% elseif not header.sortable %} no-sort{% elseif isDarkBg %} !text-primary-white{% endif %}"
                data-header-key="{{ key }}">
              {{ header.title }}
            </th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% if attr.rows|length > 0 %}
          {% for key, row in attr.rows %}
            {% set isDarkRow = loop.index % 2 != 0 %}
            <tr>
              {% for headerKey, header in attr.headers %}
                <td class="!px-4" {% if headerKey == 'price' %}data-sort="{% if row.price.promo %}{{ row.price.promo }}{% else %}{{ row.price.default }}{% endif %}"{% endif %}>
                  <div class="flex sm:justify-center sm:items-center pt-3 sm:pt-0 text-align-center text-14{% if headerKey == 'name' %} font-radikal-bold {% endif %}{% if isDarkBg and not isDarkRow %} text-primary-white{% endif %}">
                    {% if row[headerKey] %}
                      {% if headerKey == 'price' and row.price.default %}
                        {{ price.data({ defaultPrice: row.price.default, promo: row.price.promo, display: 'small', hideLabel: true, prefix:"", strikeThroughColor: "#FF565A"}) }}
                      {% else %}
                      {{ row[headerKey] | raw }}
                      {% endif %}
                    {% else %}
                      &ndash;
                    {% endif %}
                  </div>
                </td>
              {% endfor %}
            </tr>
          {% endfor %}
        {% elseif attr.defaultEmtpyMessage %}
          <tr>
            <td colspan="{{ attr.headers|length }}">{{ attr.defaultEmtpyMessage | raw }}</td>
          </tr>
        {% endif %}
      </tbody>
    </table>
  </div>
{% endmacro %}
