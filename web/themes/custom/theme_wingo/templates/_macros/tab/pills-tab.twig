{#
/**
 * Theme implementation to display a tab with pills element.
 *
 * Parameters attributes:
 *  - tabItems: An array containing the information of the tab to display.
 *  - tabsGroupId: Id which makes it possible to differentiate the different tabs.
 *  - lottieAnimation: A link to a JSON file that will contain the animation.
 *  - forceOverStyle: A boolean that will make sure the unselected tab button's background is grey.
 *  - accordion: A boolean that discrimintaes between plain rendering or accordion rendering on panels content.
 *  - urlTracking: A boolean that will add a data attribute to the tab button to track the click on the tab with URL parameter.
  * - linkedTab: A string reference that is common between .
 *
 * This macro uses the aria-tablist library to manage the tabs.
 * It also use the library : pills-tab to manage the pills animation.
 *  - @see js/components/pills-tab.js
 */
#}
{% macro data(attr = {type: 'default', tabItems: [], tabsGroupId: '',  lottieAnimation: '', forceOverStyle: false, greyBackground: false, accordion: false, urlTracking: false, linkedTab: '', align: '', isDarkBg: false, additionalClasses: [], behaviour: 'default' }) %}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% import '@theme_wingo/_macros/tab/smartphone-detail-tab.twig' as smartphone %}
  {% import '@theme_wingo/_macros/accordion/accordion.twig' as accordion %}
  {% set paragraph_position_exceptions = ['tv_channels','radio_channels'] %}
  {% set isDarkBg = attr.isDarkBg %}
  {% if attr.tabItems is defined and attr.tabItems|length > 1 %}
    <div data-scrollable-tab="{{ attr.type }}" data-additional-classes="{{ attr.additionalClasses|json_encode }}"
         class="scroll-smooth overflow-x-auto flex relative flex-col justify-center max-w-[100vw] !overflow-y-hidden {%- if attr.align == "left" %} mr-auto px-6 sm:px-0{% endif %} {% if attr.type == 'default' -%} py-2.5 ml-6 sm:ml-0 {% endif %} ">
      {% if attr.type != 'secondary' %}
        <div right-hint
             class="scroll-smooth content flex items-center h-11 w-12 absolute hidden top-[29%] right-0 justify-end z-30 bg-gradient-to-l from-primary-white to-transparent"></div>
        <div left-hint
             class="scroll-smooth content flex items-center h-11 w-12 absolute hidden top-[29%] left-0 justify-start z-30 bg-gradient-to-r from-primary-white to-transparent"></div>
      {% endif %}
      <div
        role="tablist" {% if attr.type == 'default' -%} data-pill-tablist {% else -%} data-alternative-tablist {%- endif %}
        aria-label="tabs"
        class="flex relative mx-auto h-12 items-center px-1 transition-opacity delay-100 {% if attr.type == 'default' -%} rounded-full opacity-0 mb-5 mt-5 {% else -%} mb-5 mt-5 {%- endif %}{% if attr.greyBackground and attr.type == 'default' -%} bg-grey-lighter {%- elseif not attr.greyBackground and attr.type == "default" %} bg-primary-white {%- endif %}">
        {% if attr.type == 'default' -%}
        <div
          class="z-10 flex transition-all absolute my-auto top-0 bottom-0 left-0 bg-primary-red w-full h-[calc(100%-8px)]  {% if attr.type == 'default' -%} rounded-full"
          data-pill-indicator {%- else %}" {%- endif %}>
      </div>
      {%- endif %}
      {% set ariaControls = '' %}
      {% for tab in attr.tabItems %}
        <button role="tab" id="tab-{{ attr.tabsGroupId }}-{{ loop.index }}" linked_tab='{{ attr.linkedTab }}'
                data-tab-id="{{ tab.id }}" data-control-content="panel-{{ loop.index }}"
                aria-controls="{{ ariaControls }}"
                {% if attr.behaviour == 'alternate' %}data-behaviour="{{ attr.behaviour }}"{% endif %}
                class="outline-0 whitespace-nowrap transition-colors relative block h-10{% if attr.forceOverStyle -%} bg-grey-lighter {%- elseif not attr.forceOverStyle and attr.type == "default" %} hover:bg-grey-lighter {%- endif %} {% if attr.type == 'default' -%} rounded-full text-primary-black px-6 mr-1 {% elseif attr.type == 'secondary' %} cursor-pointer rounded-full border border-primary-white text-primary-white mr-2 px-5{%- else %} mx-6 border-b-2 mb-0.5 border-none{%- endif %}{% if attr.behaviour == 'alternate' %} border-2{% endif %}" {% if attr.type == 'default' -%} data-pill-tab {%- else %} data-alternative-tab {%- endif %} {% if attr.urlTracking -%} parameter-url-track data-url-tracking="{{ tab.safe_name }}" {%- endif %}>
          <div class="flex gap-1 align-center pr-2">
            {% if tab.img %}
              <img class="w-4 h-4 transition-all duration-300 transform"
                   src="/{{ active_theme_path() }}/dist/img/{{ tab.img }}"
                   aria-hidden="true" width="16" height="16">
            {% endif %}
            <h3 class="relative z-10 text-16 font-radikal-bold {% if attr.type == 'secondary' %}pr-2{% endif %}">{{ tab.title }}</h3>
          </div>
        </button>
      {% endfor %}
    </div>
    </div>
  {% endif %}
  {% if attr.lottieAnimation %}
    {{ attach_library('theme_wingo/lottie-player') }}
    <div class="flex items-center justify-center my-10">
      <lottie-player class="max-w-sm" src="{{ attr.lottieAnimation }}"></lottie-player>
    </div>
  {% endif %}
  <div class="relative {% if attr.type == 'secondary' %} hidden {% endif %}">
    {% for tab in attr.tabItems %}
      {% set ariaControls = 'panel-' ~ attr.tabsGroupId ~ '-' ~ loop.index %}
      {% if tab.content %}
        {% set path_template = '@theme_wingo/templates/paragraph/paragraph--' ~ tab.content[0].type|replace({'_':'-'}) ~ '.html.twig' %}
      {% endif %}
      {% set groupId = attr.tabsGroupId ~ '-' ~ loop.index %}
      {% set panelGroupId = 'panel-' ~ groupId %}
      {% set tabGroupId = 'tab-' ~ groupId %}
      <div role="tabpanel" aria-labelledby="{{ tabGroupId }}"
           id="{{ panelGroupId }}"
           class="text-20 sm:text-24">
        {% if attr.accordion %}
          {{ accordion.data({items: tab.text, border: true}) }}
        {% else %}
          {% if tab.sub_tab is not empty %}
            <div>
              <div class="mx-auto flex justify-center text-left" data-pill-container data-tab-id="{{ loop.index }}">
                <div
                  class="sm:mx-auto flex flex-col justify-center md:px-0 {% if attr.type != 'default' -%} my-8 {%- endif %}">
                  {{ _self.data({type : 'alternative', tabItems: tab.sub_tab, tabsGroupId: loop.index, urlTracking : attr.urlTracking, linkedTab : ariaControls  }) }}
                </div>
              </div>
            </div>
          {% elseif tab.sub_tab is empty and tab.tab is not empty %}
            <div>
              <div class="mx-auto flex justify-center text-left max-w-[100vw] content-grid">
                <div class="sm:mx-auto flex flex-col justify-center {% if attr.type != 'default' -%} my-8 {%- endif %}">
                  <div class="mb-7 text-base {% if isDarkBg %}text-primary-white{% endif %}">{{ tab.tab | raw }}</div>
                </div>
              </div>
            </div>
          {%- endif %}
          {% if tab.smartphone_new_abo %}
            {% set imagePath = tab.smartphone_new_abo.handset_images ? tab.smartphone_new_abo.handset_images.image.0.path : tab.smartphone_new_abo.image %}
            {% set productSummary = {
              memory: tab.smartphone_new_abo.memory_id,
              color: tab.smartphone_new_abo.color_data.name,
              monthly_charge: tab.smartphone_new_abo.monthly_charge,
              street_price: tab.smartphone_new_abo.z_street_price,
              number_of_installments: tab.smartphone_new_abo.number_of_installments,
              upfront_fee: tab.smartphone_new_abo.upfront_fee } %}
            {{ smartphone.data({
              display: "productList",
              image: imagePath,
              productSummary: productSummary,
              title: tab.smartphone_new_abo.marketing_name,
              checkoutPath: tab.smartphone_new_abo.parent_data.content.smartphones.new_customer.checkout_path,
              listProduct: tab.smartphone_new_abo.parent_data.content.smartphones.new_customer.products,
              id: attr.tabsGroupId,
              smartphoneData: { current: { color: tab.smartphone_new_abo.color_data, memory: tab.smartphone_new_abo.memory_data }, colors: tab.smartphone_new_abo.colors, memories: tab.smartphone_new_abo.memories } }) }}
          {% elseif tab.smartphone_existing_abo %}
            {{ smartphone.data({ display: "simple", customerData : tab.smartphone_existing_abo }) }}
          {% else %}
            {% if tab.content and tab.content[0].type not in paragraph_position_exceptions %}
              {% include path_template with {
                formatted_data: tab.content.0,
                img_centered: true,
                group_id: tab.id
              } %}
            {% endif %}
            {% if tab.type == "articles" %}
              <div
                class="px:0 sm:px-auto overflow-x-auto flex flex-col justify-center max-w-[100vw] content-grid flex flex-col items-center">
                <div class="default-grid my-7 text-center">
                  <div class="col-span-12 {% if isDarkBg %}text-primary-white{% endif %}">
                    {{ tab.text | raw }}
                  </div>
                </div>
              </div>
            {% else %}
              <div class="my-7 flex flex-col items-center">
                <div
                  class="max-w-3xl text-center text-base padding-bottom mx-6 sm:mx-0{% if isDarkBg %} text-primary-white{% endif %}">
                  {{ tab.text | raw }}
                </div>
              </div>
            {% endif %}
          {% endif %}
        {% endif %}
        {% if tab.cta %}
          <div class="flex items-center justify-center">
            {{ trigger.data({variant: tab.cta.variant, disabled: false, label: tab.cta.title, path: tab.cta.path, target: tab.cta.target, icon: tab.cta.icon, additionalClasses : tab.cta.tracking_class, negative: isDarkBg}) }}
          </div>
        {% endif %}
        {% if tab.content and tab.content[0].type in paragraph_position_exceptions %}
          {% include path_template with {
            formatted_data: tab.content.0,
            group_id: tab.id
          } %}
        {% endif %}
      </div>
    {% endfor %}
  </div>
{% endmacro %}
