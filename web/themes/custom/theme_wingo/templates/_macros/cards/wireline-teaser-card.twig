{% macro data(attr = {title: "", description: "", price: {}, usp: "", conditions: "", display: "", isAvailableOffers: false}) %}
  {% import '@theme_wingo/_macros/prices/price.twig' as price %}
  {% import '@theme_wingo/_macros/buttons/trigger.twig' as trigger %}
  {% set promo = (attr.productVariant.price.promo and attr.productVariant.price.promo != attr.productVariant.price.default) %}
  {% if attr.productVariant.bg_color %}
    {% set backgroundColor = attr.productVariant.bg_color %}
  {% elseif not attr.productVariant.bg_color and productVariant.price.promo is not null and productVariant.price.promo != productVariant.price.default and attr.paragraph == "offer" %}
    {% set backgroundColor = "#FF565A"  %}
  {% endif %}
  {% if backgroundColor is dark_bg or backgroundColor == "#FF565A" %}
    {% set is_dark_bg = true %}
  {% endif %}
  {% set translationOptions = { } %}
  {% if attr.translationOptions %}
    {% set translationOptions = attr.translationOptions %}
  {% endif %}
  {% set borderColor = get_usp_border_color(attr.productVariant.border_color, attr.usp, promo) %}
  <div {% if attr.productVariant.bundle and attr.display == "productConfigurator" %}data-bundle-id="{{ attr.productVariant.bundle.uuid }}"{% endif %}data-final-price='{{ (promo ) ? attr.productVariant.price.promo : attr.productVariant.price.default }}' {% if attr.type == "product" %}data-url="{{ attr.productVariant.cta.path }}" data-product-id="{{ attr.productVariant.id }}"{% else %} data-option-id="{{ attr.id }}"{% endif %} {% if attr.usp %}data-usp {% endif %}class="sm:min-h-[260px] sm:m-2 cursor-pointer relative flex flex-col items-start h-full mt-0{% if attr.usp %} border-6 border {% elseif not attr.isAvailableOffers %} outline-2 outline outline-grey-medium {% endif %} justify-between transition duration-150 ease-in-out transform rounded-2xl transition-all p-6 pt-8 pb-4 sm:hover:!scale-[1.020] sm:!scale-[0.998] shadow-teaser-card {% if promo %} bg-primary-red text-primary-white bg-primary-red{% endif %} teaser-card-{{ attr.productVariant.id }}-{{ attr.uuidParagraph }}" style="{% if backgroundColor %}background-color:{{ backgroundColor }};{% endif %}{% if borderColor %}border-color: {{ borderColor }};{% endif %}"{% if attr.addEcommerceData and attr.productVariant.ecommerce_data %}data-ecommerce-item="{{ decorate_ecommerce_data(attr.productVariant.ecommerce_data, { index: attr.index })|json_encode(constant('JSON_FORCE_OBJECT') b-or constant('JSON_UNESCAPED_SLASHES') b-or constant('JSON_UNESCAPED_UNICODE')) }}" {% endif %}>
    <div class="mb-9">
      <h3 class="text-16 font-radikal-regular{% if promo or is_dark_bg %} text-grey-white{% else %} text-grey-dark{% endif %}">{{ attr.title|raw }}</h3>
      <div class="{% if attr.isAvailableOffers %}text-24 md:text-32 {% else %}text-28 {% endif %}font-radikal-bold">{{ attr.productVariant.speed ? attr.productVariant.speed|raw : attr.productVariant.lead|striptags|raw }}</div>
      <div class="text-14 font-radikal-regular{% if promo or is_dark_bg %} text-grey-white{% else %} text-grey-dark{% endif %}">{{ attr.productVariant.marketing_sentence|raw|striptags }}</div>
    </div>
    {% if attr.usp %}
      <div class="py-1 px-1.5 absolute -top-0.5 -left-0.5 rounded" style="background-color:{{ borderColor }}">
        <div data-usp class="text-12 font-radikal-bold {% if borderColor is dark_bg %} text-primary-white {% else %}text-primary-black{% endif %}">{{ attr.usp|raw }}</div>
      </div>
    {% endif %}
    {% if attr.productVariant.price.promo_percentage %}
      <p class="{% if attr.display == "productConfigurator" %}text-18 px-1.5{% else %}text-32 px-2 pt-1{% endif %} font-radikal-bold text-primary-red bg-secondary-green rounded-lg mb-1" {% if attr.productVariant.promo_color %}style="background-color: {{ attr.productVariant.promo_color }}"{% endif %}>-{{ attr.productVariant.price.promo_percentage }}%</p>
    {% endif %}
    <div {% if not attr.productVariant.price.promo_percentage %} class="mt-auto"{% endif %}>
      {{ price.data({ defaultPrice: attr.productVariant.price.default, promo: attr.productVariant.price.promo, translationOptions: translationOptions, display: attr.priceResize ? "semi-large" : "large", strikeThroughColor: "#161E22" }) }}
    </div>
    <div class="text-14 font-radikal-regular{% if promo %} text-grey-white{% else %} text-grey-dark{% endif %}">{{ attr.productVariant.specific_conditions|raw }}</div>
    {% if attr.ctaList %}
      <div data-teaser-card-cta class="mt-4 flex {% if attr.showImage %}sm:w-5/12 w-full{% else %}w-full{% endif %}">
        {% set primaryCta = attr.ctaList|first == "primary" ? true : false %}
        {% if attr.ctaList|length == 1 %}
          {% set primaryCta = attr.ctaList|first == "primary" ? true : false %}
          {{ trigger.data({variant: attr.ctaList|first, negative: (promo), label: primaryCta ? attr.productVariant.cta.title : attr.productVariant.cta_secondary.title, path: primaryCta ? attr.productVariant.cta.path : attr.productVariant.cta_secondary.path, target:  primaryCta ? attr.productVariant.cta.target : attr.productVariant.cta_secondary.target, icon: primaryCta ? attr.productVariant.cta.icon_primary : attr.productVariant.cta_secondary.icon | default("plus-sign"), additionalClasses: cta.tracking_class ~ " !w-full !justify-center", width: "full" }) }}
        {% elseif attr.ctaList|length > 1 %}
          {% for cta in attr.ctaList %}
            <div class="{% if loop.last %}mb-0 {% endif %}my-3 flex justify-center">
              {{ trigger.data({variant: cta, label: loop.index == 1 ? attr.productVariant.cta.title : attr.productVariant.cta_secondary.title, path: loop.index == 1 ? attr.productVariant.cta.path : attr.productVariant.cta_secondary.path,target: loop.index == 1 ? attr.productVariant.cta.target : attr.productVariant.cta_secondary.target,icon: loop.index == 1 ? attr.productVariant.cta.icon_primary | default("plus-sign") : attr.productVariant.cta_secondary.icon | default("plus-sign") ,additionalClasses: cta != "tertiary" ? cta.tracking_class ~ " !w-full !justify-center" : "",width: cta != "tertiary" ? "full" : ""}) }}
            </div>
          {% endfor %}
        {% endif %}
      </div>
    {% endif %}
    {% if attr.showImage and attr.decorationInContext.type == "video" %}
      <div class="absolute top-0 left-0 right-0 mx-auto w-full h-full -z-10 flex rounded-2xl overflow-hidden">
        <video class="w-full object-cover hidden sm:block rounded-2xl" autoplay muted loop>
          <source src="{{ attr.decorationInContext.path }}" type="video/mp4">
        </video>
      </div>
    {% endif %}
  </div>
  {% if attr.showImage and attr.decorationInContext.type == "image"  %}
    <style>
      @media only screen and (min-width: 767px) {
        .teaser-card-{{ attr.id }}-{{ attr.uuidParagraph }} {
          background-image: url({{ attr.decorationInContext.path.fallback_image }});
          background-size: contain;
          background-repeat: no-repeat;
          background-position: right bottom;
        }
      }
    </style>
  {% endif %}
{% endmacro %}
