{#
/**
 * @file
 * Theme override for a set of links.
 *
 * Available variables:
 * - attributes: Attributes for the UL containing the list of links.
 * - links: Links to be output.
 *   Each link will have the following elements:
 *   - link: (optional) A render array that returns a link. See
 *     template_preprocess_links() for details how it is generated.
 *   - text: The link text.
 *   - attributes: HTML attributes for the list item element.
 *   - text_attributes: (optional) HTML attributes for the span element if no
 *     'url' was supplied.
 * - heading: (optional) A heading to precede the links.
 *   - text: The heading text.
 *   - level: The heading level (e.g. 'h2', 'h3').
 *   - attributes: (optional) A keyed list of attributes for the heading.
 *   If the heading is a string, it will be used as the text of the heading and
 *   the level will default to 'h2'.
 *
 *   Headings should be used on navigation menus and any list of links that
 *   consistently appears on multiple pages. To make the heading invisible use
 *   the 'visually-hidden' CSS class. Do not use 'display:none', which
 *   removes it from screen readers and assistive technology. Headings allow
 *   screen reader and keyboard only users to navigate to or skip the links.
 *   See http://juicystudio.com/article/screen-readers-display-none.php and
 *   http://www.w3.org/TR/WCAG-TECHS/H42.html for more information.
 *
 * @see template_preprocess_links()
 */
#}
{{ attach_library('theme_wingo/single-select') }}
{% import '@theme_wingo/form/select.html.twig' as select %}
{% if links -%}
  <select {{ attributes }} name="language-switcher" data-single-select>
    {%- for item in links -%}
      <option{{ item.attributes }} {% if current_lang.code == get_language_id(item.link['#options'].language) -%} selected="selected" {%- endif %} value="{{ item.link['#options'].attributes.title }}" data-navigate="{{ path_by_language(item.link['#url'].routeName, item.link['#url'].routeParameters, item.link['#options']) }}">
          {{ item.text }}
      </option>
    {%- endfor -%}
  </select>
{%- endif %}
