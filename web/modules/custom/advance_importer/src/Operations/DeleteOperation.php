<?php

namespace Drupal\advance_importer\Operations;

/**
 * Provides the Advance Importer plugin manager.
 */
class DeleteOperation {

  protected const BUNDLE = ['node'=>'nid','taxonomy_term'=>'tid'];

  /**
   * Update function
   * @return Boolean
   * */
  public static function execute($fields,$configuration){
    $entity_type = $configuration['entity_type'];
    $bundle = $configuration['entity_type_bundle'];
    $id = $fields[self::BUNDLE[$entity_type]];
    $entity = \Drupal::entityTypeManager()->getStorage($entity_type)->load($id);
    if(empty($entity)){
      return false;
    }
    $entity->delete();
    return true;
  }
}
