<?php

namespace Drupal\advance_importer\Operations;

use Drupal;
use Drupal\Core\Entity\EntityInterface;
use Drupal\field\Entity\FieldConfig;

/**
 * Provides the Advance Importer plugin manager.
 */
class BaseOperation {

  protected const FIELD_KEY = ['node' => 'title', 'taxonomy_term' => 'name', 'user' => 'mail', 'block_content' => 'info'];

  protected const ENTITY_TYPES = ['node' => 'type', 'taxonomy_term' => 'vid'];

  protected const VALID_FIELD_MISC = ['nid', 'tid', 'title', 'name', 'description', 'langcode'];

  protected $HEADER_KEY_VALUE_DELIMITER;

  protected $MULTI_DELIMITER;

  /**
   *
   */
  public function __construct($HEADER_KEY_VALUE_DELIMITER, $MULTI_DELIMITER) {
    $this->HEADER_KEY_VALUE_DELIMITER = $HEADER_KEY_VALUE_DELIMITER;
    $this->MULTI_DELIMITER = $MULTI_DELIMITER;
  }

  /**
   * {@inheritdoc}
   * use \Drupal\advance_importer\Plugin\ProcessorBase
   * Export Operation
   */
  public function createData($start, $end, $configuration) {
    $header = [];
    $data = [];
    $data_tmp = [];

    $ids = Drupal::entityQuery($configuration['entity_type'])
      ->accessCheck()
      ->condition(self::ENTITY_TYPES[$configuration['entity_type']], $configuration['entity_type_bundle'])
      ->range($start, ($end - $start))
      ->execute();

    if (empty($ids)) {
      return [$header, $data];
    }

    $field_entities = Drupal::service('entity_field.manager')->getFieldDefinitions($configuration['entity_type'], $configuration['entity_type_bundle']);
    $entities = Drupal::entityTypeManager()->getStorage($configuration['entity_type'])->loadMultiple($ids);

    $valid_fields = Drupal::service('config.factory')->getEditable('advance_importer.settings')->get('valid_fields_' . $configuration['entity_type']);
    $valid_fields = json_decode($valid_fields, TRUE);
    $valid_fields = $this->transformMachineName($valid_fields, $configuration);

    // Header in first run
    if ($start === 0) {
      foreach (reset($entities) as $machine_name => $value) {
        if (!in_array($machine_name, $valid_fields)) {
          continue;
        }
        $label = $field_entities[$machine_name] instanceof FieldConfig ? $field_entities[$machine_name]->label() : $field_entities[$machine_name]->getName();
        $header[] = $machine_name . $this->HEADER_KEY_VALUE_DELIMITER . $label;
      }
    }

    $langcodes = Drupal::service('wimc_admin_core')->getAvailableLanguages();
    foreach ($entities as $entity) {
      // Default entity row
      $fields = [];
      $entity_langcode = $entity->language()->getId();
      foreach ($entity as $machine_name => $value) {
        if (!in_array($machine_name, $valid_fields)) {
          continue;
        }
        $fields[] = $machine_name;
        $fieldEntity = $field_entities[$machine_name];
        $label = $fieldEntity instanceof FieldConfig ? $fieldEntity->label() : $fieldEntity->getName();
        $field_key = $machine_name . $this->HEADER_KEY_VALUE_DELIMITER . $label;
        // Paragraph
        if ($fieldEntity->getType() === 'entity_reference_revisions' && $machine_name === 'field_cm_content') {
          $data_tmp[$field_key] = $this->getParagraphFieldValue($entity, $machine_name);
        } else {
          $data_tmp[$field_key] = $this->getReferenceTitle($fieldEntity, $value->getValue(), $entity_langcode);
        }

      }
      $data[] = $data_tmp;
      // Translation row
      foreach ($langcodes as $langcode) {
        if ($langcode === $entity_langcode || !$entity->hasTranslation($langcode)) {
          continue;
        }
        $translated_entity = $entity->getTranslation($langcode);
        foreach ($fields as $field ) {
          $fieldEntity = $field_entities[$field];
          $label = $fieldEntity instanceof FieldConfig ? $fieldEntity->label() : $fieldEntity->getName();
          $field_key = $field . $this->HEADER_KEY_VALUE_DELIMITER . $label;
          if ($fieldEntity->getType() === 'entity_reference_revisions' && $field === 'field_cm_content') {
            $data_tmp[$field_key] = $this->getParagraphFieldValue($translated_entity, $field, $langcode);
          } else {
            $data_tmp[$field_key] = $this->getReferenceTitle($fieldEntity, $translated_entity->get($field)
              ->getValue(), $langcode);
          }
        }
        $data[] = $data_tmp;
      }
    }

    return [$header, $data];
  }

  /**
   *
   */
  protected function transformMachineName($valid_fields, $configuration) {
    $tmp = [];
    foreach ($valid_fields as $machine_name => $value) {
      if ($value == 0) {
        continue;
      }
      $tmp[] = str_replace($configuration['entity_type'] . '.', '', $machine_name);
    }
    return array_merge($tmp, self::VALID_FIELD_MISC);
  }

  /**
   * @param \Drupal\Core\Entity\EntityInterface $entity
   * @param string $machine_name
   *
   * @return string
   * @throws \Drupal\Core\TypedData\Exception\MissingDataException
   */
  protected function getParagraphFieldValue(EntityInterface $entity, string $machine_name, string $langcode=''): string {
    $pEntities = $entity->get($machine_name)?->referencedEntities();
    if (empty($pEntities)) {
      return '';
    }
    /** @var \Drupal\paragraphs\Entity\Paragraph $pEntity */
    foreach ($pEntities as $pEntity) {
      if ($pEntity->hasTranslation($langcode)) {
        $pEntity = $pEntity->getTranslation($langcode);
      }
      if (!$pEntity->get('field_cm_text')->isEmpty()) {
        $text = $pEntity->get('field_cm_text')->first();
        return trim(check_markup($text->value, $text->format)->__toString());
      }
    }
    return '';
  }

  /**
   * @param $field_entity
   * @param $target_ids
   * @param string $langcode
   *
   * @return string|null
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  protected function getReferenceTitle($field_entity, $target_ids, string $langcode='') {
    if (empty($target_ids)) {
      return NULL;
    }
    $fields = [];

    foreach ($target_ids as $target_id) {
      $tmp_fields = !empty($target_id['value']) ? $target_id['value'] : FALSE;
      if (empty($tmp_fields)) {
        $tmp_fields = !empty($target_id['target_id']) ? $target_id['target_id'] : FALSE;
      }
      $fields[] = $tmp_fields;
    }

    if ($field_entity->getType() == 'entity_reference') {
      $entity_type = $field_entity->getSettings()['target_type'];

      if (empty(self::FIELD_KEY[$field_entity->getSettings()['target_type']])) {
        return implode($this->MULTI_DELIMITER, $fields);
      }

      $fields = [];

      foreach ($target_ids as $target_id) {
        $id = !empty($target_id['value']) ? $target_id['value'] : $target_id['target_id'];
        $target_id_values = Drupal::entityTypeManager()
          ->getStorage($field_entity->getSettings()['target_type'])
          ->load($id);
        if (empty($target_id_values)) {
          continue;
        }
        $target_id_values = $target_id_values->get(self::FIELD_KEY[$field_entity->getSettings()['target_type']]);
        $target_id_values = !empty($target_id_values->getValue()) ? $target_id_values->getValue()[0]['value'] : FALSE;
        $fields[] = $target_id_values;
      }
    }

    if ($field_entity->getType() == 'file' || $field_entity->getType() == 'image') {
      $fields = [];
      foreach ($target_ids as $target_id) {
        $target_id_value = Drupal::entityTypeManager()->getStorage('file')->load($target_id['target_id'])->getFileUri();
        $fields[] = str_replace('public://', '/sites/default/files/', $target_id_value);
      }
    }
    return implode($this->MULTI_DELIMITER, $fields);
  }

  /**
   * {@inheritdoc}
   * use \Drupal\advance_importer\Plugin\ProcessorBase
   * Import Operation
   */
  public function createOperation($header, $datas, &$context, $configuration) {
    $field_entities = Drupal::service('entity_field.manager')->getFieldDefinitions($configuration['entity_type'], $configuration['entity_type_bundle']);
    $this->detectInvalidHeaders($header, $field_entities, $context);
    foreach ($datas as $data) {
      [$is_added, $is_updated, $is_deleted, $fields] = $this->getFields($header, $data, $field_entities);

      if ($is_added) {
        AddOperation::execute($fields, $configuration) ? $context['results']['added'][] = '' : FALSE;
      }
      if ($is_updated) {
        UpdateOperation::execute($fields, $configuration) ? $context['results']['updated'][] = '' : FALSE;
      }
      if ($is_deleted) {
        DeleteOperation::execute($fields, $configuration) ? $context['results']['deleted'][] = '' : FALSE;
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  protected function detectInvalidHeaders($header, $field_entities, &$context) {
    $invalidHeaders = [];
    foreach ($header as $machine_name) {
      $machine_name = explode($this->HEADER_KEY_VALUE_DELIMITER, $machine_name)[0];
      if ($machine_name == 'delete') {
        continue;
      }
      if (empty($field_entities[$machine_name])) {
        $invalidHeaders[] = $machine_name;
      }
    }
    if (!empty($invalidHeaders)) {
      $context['results']['skip'] = implode(',', $invalidHeaders);
    }
  }

  /**
   * {@inheritdoc}
   */
  protected function getFields($header, $data, $field_entities) {
    $is_added = TRUE;
    $is_updated = FALSE;
    $is_deleted = FALSE;

    $head = array_keys($data);
    if (in_array('delete', $head)) {
      $flag = $data['delete'];
      if ($flag == 1 || $flag) {
        $is_added = FALSE;
        $is_deleted = TRUE;
        $fields = [];
        foreach ($data as $key => $field_value) {
          $machine_name = explode($this->HEADER_KEY_VALUE_DELIMITER, $key)[0];
          $fields[$machine_name] = $field_value;
        }
        return [$is_added, $is_updated, $is_deleted, $fields];
      }
    }

    $fields = [];

    foreach ($data as $key => $field_value) {
      $machine_name = explode($this->HEADER_KEY_VALUE_DELIMITER, $key)[0];

      // Make sure importer still running.
      if (!in_array($machine_name, array_keys($field_entities))) {
        continue;
      }

      $field_entity = $field_entities[$machine_name];
      if (($machine_name == 'nid' || $machine_name == 'tid') && !empty($field_value)) {
        $is_updated = TRUE;
        $is_added   = FALSE;
      }

      if ($field_entity->getType() == 'entity_reference') {
        if (!in_array($field_entity->getSettings()['target_type'], array_keys(self::FIELD_KEY))) {
          $fields[] = $field_value;
          continue;
        }
        // Check if ID is provided then used it
        $field_value_clean = str_replace($this->MULTI_DELIMITER, '', $field_value);
        if (is_numeric($field_value_clean)) {
          $field_value = explode($this->MULTI_DELIMITER, $field_value);
        } else {
          $field_value = $this->getReferenceValues($field_entity, $field_value);
        }
      }

      if ($field_entity->getType() == 'file' || $field_entity->getType() == 'image') {
        $field_value = $this->getFileEntityValue($field_entity, $field_value);
      }

      $fields[$machine_name] = $field_value;
    }

    return [$is_added, $is_updated, FALSE, $fields];
  }

  /**
   * {@inheritdoc}
   */
  protected function getReferenceValues($field_entity, $field_value) {
    if (empty($field_value)) {
      return NULL;
    }
    $ids = [];
    $field_values = explode($this->MULTI_DELIMITER, $field_value);
    foreach ($field_values as $value) {
      $entity = Drupal::entityTypeManager()->getStorage($field_entity->getSettings()['target_type'])->loadByproperties([self::FIELD_KEY[$field_entity->getSettings()['target_type']] => $value]);
      if (empty($entity)) {
        continue;
      }
      $ids[] = reset($entity)->id();
    }
    return !empty($ids) ? $ids : NULL;
  }

  /**
   * {@inheritdoc}
   */
  protected function getFileEntityValue($field_entity, $field_value) {
    if (empty($field_value)) {
      return NULL;
    }
    $ids = [];
    $field_values = explode($this->MULTI_DELIMITER, $field_value);
    foreach ($field_values as $value) {
      if (!file_exists($this->realPath($value))) {
        continue;
      }
      $entity = Drupal::entityTypeManager()->getStorage('file')->loadByproperties(['uri' => $this->realPath($value)]);
      if (empty($entity)) {
        Drupal::entityTypeManager()->getStorage('file')->create(['uri' => $this->realPath($value)])->save();
        $entity = Drupal::entityTypeManager()->getStorage('file')->loadByproperties(['uri' => $this->realPath($value)]);
      }
      $ids[] = reset($entity)->id();
    }
    return $ids;
  }

  /**
   * {@inheritdoc}
   */
  protected function realpath($value) {
    $value = str_replace('/sites/default/files/', 'public://', $value);
    return str_replace('sites/default/files/', 'public://', $value);
  }

  /**
   * { @test }
   * */
  public function test1() {
    $header = ['title', 'field_content', 'field_file', 'field_taxonomy', 'field_block'];
    $data = ['Sample', 'Lenis Sudo', 'sites/default/files/advance_importer/test_21.csv', 'wr', 'block test'];

    $configuration['entity_type'] = 'node';
    $configuration['entity_type_bundle'] = 'importer';

    $field_entities = Drupal::service('entity_field.manager')->getFieldDefinitions($configuration['entity_type'], $configuration['entity_type_bundle']);

    return $this->getFields($header, $data, $field_entities);
  }

  /**
   * { @test }
   * */
  public function test2() {
    $file = fopen('public://advance_importer/test.csv', 'r');
    $header = fgetcsv($file, 0, ';');
    $data = fgetcsv($file, 0, ';');
    fclose($file);

    $configuration['entity_type'] = 'node';
    $configuration['entity_type_bundle'] = 'importer';

    $field_entities = Drupal::service('entity_field.manager')->getFieldDefinitions($configuration['entity_type'], $configuration['entity_type_bundle']);

    return $this->getFields($header, $data, $field_entities);
  }

}
