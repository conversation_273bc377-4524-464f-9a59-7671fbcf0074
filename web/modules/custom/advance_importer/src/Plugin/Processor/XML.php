<?php

namespace Drupal\advance_importer\Plugin\Processor;

use Drupal\advance_importer\Plugin\ProcessorBase;

/**
 * Class XML Processor.
 *
 * @Processor(
 *   id = "xml_processor",
 *   label = @Translation("XML")
 * )
 */
class XML extends ProcessorBase {

  protected const DELIMITER = ';';

  protected const EXTENSION = '.xml';

  /**
   * @return array key|value
   * */
  public function getContents($start, $end) {
    $content = [];
    $xml = simplexml_load_file($this->getFilePath());
    if (empty($xml)) {
      return FALSE;
    }
    $contents = json_decode(json_encode($xml), TRUE);
    foreach ($contents['ITEM'] as $count => $fields) {
      if ($count >= $start && $count < $end) {
        $content[] = $this->parseFields($fields);
      }
    }
    return $content;
  }

  /**
   * @return array
   * */
  protected function parseFields($fields) {
    $tmp = [];
    foreach ($fields as $field) {
      foreach ($field as $value) {
        $tmp[$value['@attributes']['machine_name']] = $value['@attributes']['value'];
      }
    }
    return $tmp;
  }

  /**
   * @return array
   * */
  public function getHeader() {
    $xml = simplexml_load_file($this->getFilePath());
    if (empty($xml)) {
      return FALSE;
    }
    $content = json_decode(json_encode($xml), TRUE);
    $headers = $content['ITEM'][0]['FIELD'];
    $tmp = [];
    foreach ($headers as $header) {
      $tmp[] = $header['@attributes']['machine_name'];
    }
    return $tmp;
  }

  /**
   * @return int
   * */
  public function getTotalRows() {
    $xml = simplexml_load_file($this->getFilePath());
    if (empty($xml)) {
      return 0;
    }
    return count($xml);
  }

  /**
   * {@inheritdoc}
   * Export
   */
  public function parseContent($data, $FILE_EXPORT, $HEADER_KEY_VALUE_DELIMITER, $MULTI_DELIMITER, $batch_contents) {
    $filename = $this->configuration['entity_type'] . '_' . $this->configuration['entity_type_bundle'] . self::EXTENSION;
    $h_file = fopen($FILE_EXPORT . $filename, 'r');
    $header = fread($h_file, 10);
    fclose($h_file);

    [$header_entity, $fields] = $data;

    $file = fopen($FILE_EXPORT . $filename, 'a');
    if (empty($header)) {
      fwrite($file, '<' . strtoupper($this->configuration['entity_type']) . '>');
    }

    foreach ($fields as $index => $field) {
      fwrite($file, '<ITEM>');
      foreach ($field as $machine_name => $value) {
        [$field_machine_name, $field_name] = explode($HEADER_KEY_VALUE_DELIMITER, $machine_name);
        $value = str_replace('<', '&lt;', $value);
        $value = str_replace('>', '&gt;', $value);
        $value = str_replace('&nbsp', ' ', $value);
        $value = str_replace('"', "'", $value);
        fwrite($file, '<FIELD machine_name="' . $field_machine_name . '" name="' . $field_name . '" value="' . $value . '"></FIELD>');
      }
      fwrite($file, '</ITEM>');
    }

    if ($batch_contents['end'] == $batch_contents['total']) {
      fwrite($file, '</' . strtoupper($this->configuration['entity_type']) . '>');
    }

    fclose($file);
  }

  /**
   * {@inheritdoc}
   * Export
   */
  public function initFile($FILE_EXPORT) {
    $filename = $this->configuration['entity_type'] . '_' . $this->configuration['entity_type_bundle'] . self::EXTENSION;
    $file = fopen($FILE_EXPORT . $filename, 'w');
    fclose($file);
  }

}
