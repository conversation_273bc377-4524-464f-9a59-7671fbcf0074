{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "848acec6730b5cd87240888722e58133", "packages": [{"name": "asm89/stack-cors", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/50f57105bad3d97a43ec4a485eb57daf347eafea", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea", "shasum": ""}, "require": {"php": "^7.3|^8.0", "symfony/http-foundation": "^5.3|^6|^7", "symfony/http-kernel": "^5.3|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/v2.2.0"}, "time": "2023-11-14T13:51:46+00:00"}, {"name": "chi-teck/drupal-code-generator", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/Chi-teck/drupal-code-generator.git", "reference": "2dbd8d231945681a398862a3282ade3cf0ea23ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chi-teck/drupal-code-generator/zipball/2dbd8d231945681a398862a3282ade3cf0ea23ab", "reference": "2dbd8d231945681a398862a3282ade3cf0ea23ab", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1.0", "psr/event-dispatcher": "^1.0", "psr/log": "^3.0", "symfony/console": "^6.3", "symfony/dependency-injection": "^6.3.2", "symfony/filesystem": "^6.3", "symfony/string": "^6.3", "twig/twig": "^3.4"}, "conflict": {"squizlabs/php_codesniffer": "<3.6"}, "require-dev": {"chi-teck/drupal-coder-extension": "^2.0.0-beta3", "drupal/coder": "8.3.23", "drupal/core": "10.3.x-dev", "ext-simplexml": "*", "phpspec/prophecy-phpunit": "^2.2", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.9", "symfony/var-dumper": "^6.4", "symfony/yaml": "^6.3", "vimeo/psalm": "^5.22.2"}, "bin": ["bin/dcg"], "type": "library", "autoload": {"psr-4": {"DrupalCodeGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal code generator", "support": {"issues": "https://github.com/Chi-teck/drupal-code-generator/issues", "source": "https://github.com/Chi-teck/drupal-code-generator/tree/3.6.1"}, "time": "2024-06-06T17:36:37+00:00"}, {"name": "composer/installers", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-24T20:46:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "consolidation/annotated-command", "version": "4.10.1", "source": {"type": "git", "url": "https://github.com/consolidation/annotated-command.git", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/annotated-command/zipball/362310b13ececa9f6f0a4a880811fa08fecc348b", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b", "shasum": ""}, "require": {"consolidation/output-formatters": "^4.3.1", "php": ">=7.1.3", "psr/log": "^1 || ^2 || ^3", "symfony/console": "^4.4.8 || ^5 || ^6 || ^7", "symfony/event-dispatcher": "^4.4.8 || ^5 || ^6 || ^7", "symfony/finder": "^4.4.8 || ^5 || ^6 || ^7"}, "require-dev": {"composer-runtime-api": "^2.0", "phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\AnnotatedCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Initialize Symfony Console commands from annotated command class methods.", "support": {"issues": "https://github.com/consolidation/annotated-command/issues", "source": "https://github.com/consolidation/annotated-command/tree/4.10.1"}, "time": "2024-12-13T19:55:40+00:00"}, {"name": "consolidation/config", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/consolidation/config.git", "reference": "597f8d7fbeef801736250ec10c3e190569b1b0ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/config/zipball/597f8d7fbeef801736250ec10c3e190569b1b0ae", "reference": "597f8d7fbeef801736250ec10c3e190569b1b0ae", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2 || ^3", "grasmash/expander": "^2.0.1 || ^3", "php": ">=7.1.3", "symfony/event-dispatcher": "^4 || ^5 || ^6"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": ">=7.5.20", "squizlabs/php_codesniffer": "^3", "symfony/console": "^4 || ^5 || ^6", "symfony/yaml": "^4 || ^5 || ^6", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/event-dispatcher": "Required to inject configuration into Command options", "symfony/yaml": "Required to use Consolidation\\Config\\Loader\\YamlConfigLoader"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provide configuration services for a commandline tool.", "support": {"issues": "https://github.com/consolidation/config/issues", "source": "https://github.com/consolidation/config/tree/2.1.2"}, "time": "2022-10-06T17:48:03+00:00"}, {"name": "consolidation/filter-via-dot-access-data", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/consolidation/filter-via-dot-access-data.git", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/filter-via-dot-access-data/zipball/cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2.0.0 || ^3.0.0", "php": ">=7.1.3"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Filter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "This project uses dflydev/dot-access-data to provide simple output filtering for applications built with annotated-command / Robo.", "support": {"source": "https://github.com/consolidation/filter-via-dot-access-data/tree/2.0.2"}, "time": "2021-12-30T03:56:08+00:00"}, {"name": "consolidation/log", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/log.git", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/log/zipball/c27a3beb36137c141ccbce0d89f64befb243c015", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015", "shasum": ""}, "require": {"php": ">=8.0.0", "psr/log": "^3", "symfony/console": "^5 || ^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"platform": {"php": "8.2.17"}}, "autoload": {"psr-4": {"Consolidation\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Improved Psr-3 / Psr\\Log logger based on Symfony Console components.", "support": {"issues": "https://github.com/consolidation/log/issues", "source": "https://github.com/consolidation/log/tree/3.1.0"}, "time": "2024-04-04T23:50:25+00:00"}, {"name": "consolidation/output-formatters", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/consolidation/output-formatters.git", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/output-formatters/zipball/5fd5656718d7068a02d046f418a7ba873d5abbfe", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2 || ^3", "php": ">=7.1.3", "symfony/console": "^4 || ^5 || ^6 || ^7", "symfony/finder": "^4 || ^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": "^7 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4 || ^5 || ^6 || ^7", "symfony/yaml": "^4 || ^5 || ^6 || ^7", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/var-dumper": "For using the var_dump formatter"}, "type": "library", "autoload": {"psr-4": {"Consolidation\\OutputFormatters\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Format text by applying transformations provided by plug-in formatters.", "support": {"issues": "https://github.com/consolidation/output-formatters/issues", "source": "https://github.com/consolidation/output-formatters/tree/4.6.0"}, "time": "2024-10-18T14:02:48+00:00"}, {"name": "consolidation/robo", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/consolidation/robo.git", "reference": "55a272370940607649e5c46eb173c5c54f7c166d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/robo/zipball/55a272370940607649e5c46eb173c5c54f7c166d", "reference": "55a272370940607649e5c46eb173c5c54f7c166d", "shasum": ""}, "require": {"consolidation/annotated-command": "^4.8.1", "consolidation/config": "^2.0.1", "consolidation/log": "^2.0.2 || ^3", "consolidation/output-formatters": "^4.1.2", "consolidation/self-update": "^2.0", "league/container": "^3.3.1 || ^4.0", "php": ">=8.0", "phpowermove/docblock": "^4.0", "symfony/console": "^6", "symfony/event-dispatcher": "^6", "symfony/filesystem": "^6", "symfony/finder": "^6", "symfony/process": "^6", "symfony/yaml": "^6"}, "conflict": {"codegyre/robo": "*"}, "require-dev": {"natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "^2", "pear/archive_tar": "^1.4.4", "phpunit/phpunit": "^7.5.20 || ^8", "squizlabs/php_codesniffer": "^3.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"natxet/cssmin": "For minifying CSS files in taskMinify", "patchwork/jsqueeze": "For minifying JS files in taskMinify", "pear/archive_tar": "Allows tar archives to be created and extracted in taskPack and taskExtract, respectively.", "totten/lurkerlite": "For monitoring filesystem changes in taskWatch"}, "bin": ["robo"], "type": "library", "autoload": {"psr-4": {"Robo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Modern task runner", "support": {"issues": "https://github.com/consolidation/robo/issues", "source": "https://github.com/consolidation/robo/tree/4.0.6"}, "time": "2023-04-30T21:49:04+00:00"}, {"name": "consolidation/self-update", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/consolidation/self-update.git", "reference": "972a1016761c9b63314e040836a12795dff6953a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/self-update/zipball/972a1016761c9b63314e040836a12795dff6953a", "reference": "972a1016761c9b63314e040836a12795dff6953a", "shasum": ""}, "require": {"composer/semver": "^3.2", "php": ">=5.5.0", "symfony/console": "^2.8 || ^3 || ^4 || ^5 || ^6", "symfony/filesystem": "^2.5 || ^3 || ^4 || ^5 || ^6"}, "bin": ["scripts/release"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"SelfUpdate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provides a self:update command for Symfony Console applications.", "support": {"issues": "https://github.com/consolidation/self-update/issues", "source": "https://github.com/consolidation/self-update/tree/2.2.0"}, "time": "2023-03-18T01:37:41+00:00"}, {"name": "consolidation/site-alias", "version": "4.1.1", "source": {"type": "git", "url": "https://github.com/consolidation/site-alias.git", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-alias/zipball/aff6189aae17da813d23249cb2fc0fff33f26d40", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40", "shasum": ""}, "require": {"consolidation/config": "^1.2.1 || ^2 || ^3", "php": ">=7.4", "symfony/filesystem": "^5.4 || ^6 || ^7", "symfony/finder": "^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": ">=7", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteAlias\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Manage alias records for local and remote sites.", "support": {"issues": "https://github.com/consolidation/site-alias/issues", "source": "https://github.com/consolidation/site-alias/tree/4.1.1"}, "time": "2024-12-13T19:05:11+00:00"}, {"name": "consolidation/site-process", "version": "5.4.2", "source": {"type": "git", "url": "https://github.com/consolidation/site-process.git", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-process/zipball/e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "shasum": ""}, "require": {"consolidation/config": "^2 || ^3", "consolidation/site-alias": "^3 || ^4", "php": ">=8.0.14", "symfony/console": "^5.4 || ^6 || ^7", "symfony/process": "^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A thin wrapper around the Symfony Process Component that allows applications to use the Site Alias library to specify the target for a remote call.", "support": {"issues": "https://github.com/consolidation/site-process/issues", "source": "https://github.com/consolidation/site-process/tree/5.4.2"}, "time": "2024-12-13T19:25:56+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.3"}, "time": "2022-12-20T22:53:13+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "time": "2024-07-08T12:26:09+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/common", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/d9ea4a54ca2586db781f0265d36bea731ac66ec5", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5", "shasum": ""}, "require": {"doctrine/persistence": "^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "doctrine/collections": "^1", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^6.1", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2025-01-01T22:12:03+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "doctrine/persistence", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/45004aca79189474f113cbe3a53847c2115a55fa", "reference": "45004aca79189474f113cbe3a53847c2115a55fa", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.6", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/4.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-11-01T21:49:07+00:00"}, {"name": "drupal/admin_toolbar", "version": "3.5.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/admin_toolbar.git", "reference": "3.5.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/admin_toolbar-3.5.3.zip", "reference": "3.5.3", "shasum": "363cdd6e6ca47983900f40793edf9a8b26f132bb"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "require-dev": {"drupal/admin_toolbar_tools": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.5.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON> (eme)", "homepage": "https://www.drupal.org/u/eme", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (romainj)", "homepage": "https://www.drupal.org/u/romainj", "role": "Maintainer"}, {"name": "<PERSON> (adriancid)", "homepage": "https://www.drupal.org/u/adriancid", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (matio89)", "homepage": "https://www.drupal.org/u/matio89", "role": "Maintainer"}, {"name": "fethi.krout", "homepage": "https://www.drupal.org/user/3206765"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "matio89", "homepage": "https://www.drupal.org/user/2320090"}, {"name": "musa.thomas", "homepage": "https://www.drupal.org/user/1213824"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/370706"}], "description": "Provides a drop-down menu interface to the core Drupal Toolbar.", "homepage": "http://drupal.org/project/admin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/admin_toolbar", "issues": "https://www.drupal.org/project/issues/admin_toolbar"}}, {"name": "drupal/advanced_text_formatter", "version": "3.0.0-rc2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/advanced_text_formatter.git", "reference": "3.0.0-rc2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/advanced_text_formatter-3.0.0-rc2.zip", "reference": "3.0.0-rc2", "shasum": "4b0601f135202647e65bd5a43f56ea1fce4bc607"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.0-rc2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "RC releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/330533"}, {"name": "knectar", "homepage": "https://www.drupal.org/user/1184414"}, {"name": "realityloop", "homepage": "https://www.drupal.org/user/139189"}, {"name": "thmnhat", "homepage": "https://www.drupal.org/user/998946"}], "description": "Formatter of textfield, text area and text format.", "homepage": "https://www.drupal.org/project/advanced_text_formatter", "support": {"source": "https://git.drupalcode.org/project/advanced_text_formatter"}}, {"name": "drupal/captcha", "version": "2.0.7", "source": {"type": "git", "url": "https://git.drupalcode.org/project/captcha.git", "reference": "2.0.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/captcha-2.0.7.zip", "reference": "2.0.7", "shasum": "8e97ba41810811bcd5d7e8b714cdc0b664dd8eec"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-1.x": "1.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1021502"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "naveenvalecha", "homepage": "https://www.drupal.org/user/2665733"}, {"name": "podarok", "homepage": "https://www.drupal.org/user/116002"}, {"name": "robloach", "homepage": "https://www.drupal.org/user/61114"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}, {"name": "wundo", "homepage": "https://www.drupal.org/user/25523"}], "description": "The CAPTCHA module provides this feature to virtually any user facing web form on a Drupal site.", "homepage": "https://www.drupal.org/project/captcha", "support": {"source": "https://git.drupalcode.org/project/captcha", "issues": "https://www.drupal.org/project/issues/captcha"}}, {"name": "drupal/config_filter", "version": "2.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_filter.git", "reference": "8.x-2.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_filter-8.x-2.7.zip", "reference": "8.x-2.7", "shasum": "7fe7161b93c7e24c6db135f1cf82d144bef66e50"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "conflict": {"drush/drush": "<10"}, "suggest": {"drupal/config_split": "Split site configuration for different environments."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "Nuvole Web", "homepage": "http://nuvole.org", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "pescetti", "homepage": "https://www.drupal.org/user/436244"}], "description": "Config Filter allows other modules to interact with a ConfigStorage through filter plugins.", "homepage": "https://www.drupal.org/project/config_filter", "keywords": ["<PERSON><PERSON><PERSON>", "configuration", "configuration management"], "support": {"source": "https://git.drupalcode.org/project/config_filter", "issues": "https://www.drupal.org/project/issues/config_filter", "slack": "https://drupal.slack.com/archives/C45342CDD"}}, {"name": "drupal/config_ignore", "version": "3.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_ignore.git", "reference": "8.x-3.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_ignore-8.x-3.3.zip", "reference": "8.x-3.3", "shasum": "4446811ecb023820a57c227d35c034e0d4363a70"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "require-dev": {"drupal/config_filter": "^1.8||^2.2", "drush/drush": "^10 || ^11 || ^12"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/tlyngej", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "role": "Maintainer"}, {"name": "tlyngej", "homepage": "https://www.drupal.org/user/413139"}], "description": "Ignore certain configuration during import and export.", "homepage": "http://drupal.org/project/config_ignore", "support": {"source": "https://git.drupalcode.org/project/config_ignore", "issues": "http://drupal.org/project/config_ignore"}}, {"name": "drupal/config_split", "version": "2.0.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_split.git", "reference": "2.0.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_split-2.0.2.zip", "reference": "2.0.2", "shasum": "efde01f790ca6f6023982e918fe51740da931d3e"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "conflict": {"drush/drush": "<10"}, "require-dev": {"drupal/config_filter": "^1||^2"}, "suggest": {"drupal/chosen": "<PERSON><PERSON> uses the Chosen jQuery plugin to make the <select> elements more user-friendly.", "drupal/select2_all": "Applies the Select2 library to all select fields on the site similar to the Chosen module."}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "Nuvole Web", "homepage": "http://nuvole.org", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "pescetti", "homepage": "https://www.drupal.org/user/436244"}], "description": "Configuration filter for importing and exporting extra config", "homepage": "https://www.drupal.org/project/config_split", "keywords": ["<PERSON><PERSON><PERSON>", "configuration", "configuration management"], "support": {"source": "https://git.drupalcode.org/project/config_split", "issues": "https://www.drupal.org/project/issues/config_split", "slack": "https://drupal.slack.com/archives/C45342CDD"}}, {"name": "drupal/content_translation_redirect", "version": "2.0.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/content_translation_redirect.git", "reference": "2.0.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/content_translation_redirect-2.0.2.zip", "reference": "2.0.2", "shasum": "ef76d80a3f1a4363044ecc906c44c81f6fdc94de"}, "require": {"drupal/core": "^9.1 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "WalkingDexter", "homepage": "https://www.drupal.org/user/3251330"}], "description": "Allows users to redirect from a non-existent content translation.", "homepage": "https://www.drupal.org/project/content_translation_redirect", "support": {"source": "https://git.drupalcode.org/project/content_translation_redirect"}}, {"name": "drupal/core", "version": "10.4.6", "source": {"type": "git", "url": "https://github.com/drupal/core.git", "reference": "c04823253d7d35c731b7b6fa1a1fddb671e984eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core/zipball/c04823253d7d35c731b7b6fa1a1fddb671e984eb", "reference": "c04823253d7d35c731b7b6fa1a1fddb671e984eb", "shasum": ""}, "require": {"asm89/stack-cors": "^2.1", "composer-runtime-api": "^2.1", "composer/semver": "^3.3", "doctrine/annotations": "^1.14", "egulias/email-validator": "^3.2.1|^4.0", "ext-date": "*", "ext-dom": "*", "ext-filter": "*", "ext-gd": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-tokenizer": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^7.5", "guzzlehttp/psr7": "^2.4.5", "masterminds/html5": "^2.7", "mck89/peast": "^1.14", "pear/archive_tar": "^1.4.14", "php": ">=8.1.0", "psr/log": "^3.0", "sebastian/diff": "^4", "symfony/console": "^6.4", "symfony/dependency-injection": "^6.4", "symfony/event-dispatcher": "^6.4", "symfony/filesystem": "^6.4", "symfony/finder": "^6.4", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.4", "symfony/mailer": "^6.4", "symfony/mime": "^6.4", "symfony/polyfill-iconv": "^1.26", "symfony/process": "^6.4", "symfony/psr-http-message-bridge": "^2.1|^6.4", "symfony/routing": "^6.4", "symfony/serializer": "^6.4", "symfony/validator": "^6.4", "symfony/yaml": "^6.4", "twig/twig": "^3.15.0"}, "conflict": {"drush/drush": "<12.4.3"}, "replace": {"drupal/core-annotation": "self.version", "drupal/core-assertion": "self.version", "drupal/core-class-finder": "self.version", "drupal/core-datetime": "self.version", "drupal/core-dependency-injection": "self.version", "drupal/core-diff": "self.version", "drupal/core-discovery": "self.version", "drupal/core-event-dispatcher": "self.version", "drupal/core-file-cache": "self.version", "drupal/core-file-security": "self.version", "drupal/core-filesystem": "self.version", "drupal/core-front-matter": "self.version", "drupal/core-gettext": "self.version", "drupal/core-graph": "self.version", "drupal/core-http-foundation": "self.version", "drupal/core-php-storage": "self.version", "drupal/core-plugin": "self.version", "drupal/core-proxy-builder": "self.version", "drupal/core-render": "self.version", "drupal/core-serialization": "self.version", "drupal/core-transliteration": "self.version", "drupal/core-utility": "self.version", "drupal/core-uuid": "self.version", "drupal/core-version": "self.version"}, "suggest": {"ext-zip": "Needed to extend the plugin.manager.archiver service capability with the handling of files in the ZIP format."}, "type": "drupal-core", "extra": {"drupal-scaffold": {"file-mapping": {"[web-root]/.htaccess": "assets/scaffold/files/htaccess", "[web-root]/README.md": "assets/scaffold/files/drupal.README.md", "[web-root]/index.php": "assets/scaffold/files/index.php", "[web-root]/.csslintrc": "assets/scaffold/files/csslintrc", "[web-root]/robots.txt": "assets/scaffold/files/robots.txt", "[web-root]/update.php": "assets/scaffold/files/update.php", "[web-root]/web.config": "assets/scaffold/files/web.config", "[web-root]/INSTALL.txt": "assets/scaffold/files/drupal.INSTALL.txt", "[web-root]/.eslintignore": "assets/scaffold/files/eslintignore", "[web-root]/.eslintrc.json": "assets/scaffold/files/eslintrc.json", "[web-root]/.ht.router.php": "assets/scaffold/files/ht.router.php", "[web-root]/sites/README.txt": "assets/scaffold/files/sites.README.txt", "[project-root]/.editorconfig": "assets/scaffold/files/editorconfig", "[web-root]/example.gitignore": "assets/scaffold/files/example.gitignore", "[web-root]/themes/README.txt": "assets/scaffold/files/themes.README.txt", "[project-root]/.gitattributes": "assets/scaffold/files/gitattributes", "[web-root]/modules/README.txt": "assets/scaffold/files/modules.README.txt", "[web-root]/profiles/README.txt": "assets/scaffold/files/profiles.README.txt", "[web-root]/sites/example.sites.php": "assets/scaffold/files/example.sites.php", "[web-root]/sites/development.services.yml": "assets/scaffold/files/development.services.yml", "[web-root]/sites/example.settings.local.php": "assets/scaffold/files/example.settings.local.php", "[web-root]/sites/default/default.services.yml": "assets/scaffold/files/default.services.yml", "[web-root]/sites/default/default.settings.php": "assets/scaffold/files/default.settings.php"}}}, "autoload": {"files": ["includes/bootstrap.inc"], "psr-4": {"Drupal\\Core\\": "lib/Drupal/Core", "Drupal\\Component\\": "lib/Drupal/Component"}, "classmap": ["lib/Drupal.php", "lib/Drupal/Component/DependencyInjection/Container.php", "lib/Drupal/Component/DependencyInjection/PhpArrayContainer.php", "lib/Drupal/Component/FileCache/FileCacheFactory.php", "lib/Drupal/Component/Utility/Timer.php", "lib/Drupal/Component/Utility/Unicode.php", "lib/Drupal/Core/Cache/Cache.php", "lib/Drupal/Core/Cache/CacheBackendInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumTrait.php", "lib/Drupal/Core/Cache/CacheTagsInvalidatorInterface.php", "lib/Drupal/Core/Cache/DatabaseBackend.php", "lib/Drupal/Core/Cache/DatabaseCacheTagsChecksum.php", "lib/Drupal/Core/Database/Connection.php", "lib/Drupal/Core/Database/Database.php", "lib/Drupal/Core/Database/StatementInterface.php", "lib/Drupal/Core/DependencyInjection/Container.php", "lib/Drupal/Core/DrupalKernel.php", "lib/Drupal/Core/DrupalKernelInterface.php", "lib/Drupal/Core/Installer/InstallerRedirectTrait.php", "lib/Drupal/Core/Site/Settings.php", "lib/Drupal/Component/Datetime/Time.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal is an open source content management platform powering millions of websites and applications.", "support": {"source": "https://github.com/drupal/core/tree/10.4.6"}, "time": "2025-04-02T21:06:44+00:00"}, {"name": "drupal/core-composer-scaffold", "version": "10.4.6", "source": {"type": "git", "url": "https://github.com/drupal/core-composer-scaffold.git", "reference": "db17b59620ce1c142a34dc017d9e696ce4771e55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-composer-scaffold/zipball/db17b59620ce1c142a34dc017d9e696ce4771e55", "reference": "db17b59620ce1c142a34dc017d9e696ce4771e55", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "require-dev": {"composer/composer": "^1.8@stable"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A flexible Composer project scaffold builder.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-composer-scaffold/tree/10.4.6"}, "time": "2024-08-22T14:31:30+00:00"}, {"name": "drupal/core-project-message", "version": "10.4.6", "source": {"type": "git", "url": "https://github.com/drupal/core-project-message.git", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-project-message/zipball/d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\ProjectMessage\\MessagePlugin"}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\ProjectMessage\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Adds a message after Composer installation.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-project-message/tree/11.1.6"}, "time": "2023-07-24T07:55:25+00:00"}, {"name": "drupal/core-recommended", "version": "10.4.6", "source": {"type": "git", "url": "https://github.com/drupal/core-recommended.git", "reference": "661bdf9cc7123f63257acb624dbb09fcb31b6a67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-recommended/zipball/661bdf9cc7123f63257acb624dbb09fcb31b6a67", "reference": "661bdf9cc7123f63257acb624dbb09fcb31b6a67", "shasum": ""}, "require": {"asm89/stack-cors": "~v2.2.0", "composer/semver": "~3.4.3", "doctrine/annotations": "~1.14.4", "doctrine/deprecations": "~1.1.3", "doctrine/lexer": "~2.1.1", "drupal/core": "10.4.6", "egulias/email-validator": "~4.0.2", "guzzlehttp/guzzle": "~7.9.2", "guzzlehttp/promises": "~2.0.4", "guzzlehttp/psr7": "~2.7.0", "masterminds/html5": "~2.9.0", "mck89/peast": "~v1.16.3", "pear/archive_tar": "~1.5.0", "pear/console_getopt": "~v1.4.3", "pear/pear-core-minimal": "~v1.10.16", "pear/pear_exception": "~v1.0.2", "psr/cache": "~3.0.0", "psr/container": "~2.0.2", "psr/event-dispatcher": "~1.0.0", "psr/http-client": "~1.0.3", "psr/http-factory": "~1.1.0", "psr/log": "~3.0.2", "ralouphie/getallheaders": "~3.0.3", "sebastian/diff": "~4.0.6", "symfony/console": "~v6.4.15", "symfony/dependency-injection": "~v6.4.16", "symfony/deprecation-contracts": "~v3.5.1", "symfony/error-handler": "~v6.4.14", "symfony/event-dispatcher": "~v6.4.13", "symfony/event-dispatcher-contracts": "~v3.5.1", "symfony/filesystem": "~v6.4.13", "symfony/finder": "~v6.4.13", "symfony/http-foundation": "~v6.4.16", "symfony/http-kernel": "~v6.4.16", "symfony/mailer": "~v6.4.13", "symfony/mime": "~v6.4.13", "symfony/polyfill-ctype": "~v1.31.0", "symfony/polyfill-iconv": "~v1.31.0", "symfony/polyfill-intl-grapheme": "~v1.31.0", "symfony/polyfill-intl-idn": "~v1.31.0", "symfony/polyfill-intl-normalizer": "~v1.31.0", "symfony/polyfill-mbstring": "~v1.31.0", "symfony/polyfill-php83": "~v1.31.0", "symfony/process": "~v6.4.15", "symfony/psr-http-message-bridge": "~v6.4.13", "symfony/routing": "~v6.4.16", "symfony/serializer": "~v6.4.15", "symfony/service-contracts": "~v3.5.1", "symfony/string": "~v6.4.15", "symfony/translation-contracts": "~v3.5.1", "symfony/validator": "~v6.4.16", "symfony/var-dumper": "~v6.4.15", "symfony/var-exporter": "~v6.4.13", "symfony/yaml": "~v6.4.13", "twig/twig": "~v3.19.0"}, "conflict": {"webflo/drupal-core-strict": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Core and its dependencies with known-compatible minor versions. Require this project INSTEAD OF drupal/core.", "support": {"source": "https://github.com/drupal/core-recommended/tree/10.4.6"}, "time": "2025-04-02T21:06:44+00:00"}, {"name": "drupal/crop", "version": "2.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/crop.git", "reference": "8.x-2.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/crop-8.x-2.4.zip", "reference": "8.x-2.4", "shasum": "be11fad0abf1d53544d35cb4ca6cedd8e91d2542"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides storage and API for image crops.", "homepage": "https://www.drupal.org/project/crop", "support": {"source": "https://git.drupalcode.org/project/crop", "issues": "https://www.drupal.org/project/issues/crop"}}, {"name": "drupal/cshs", "version": "4.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/cshs.git", "reference": "4.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/cshs-4.0.3.zip", "reference": "4.0.3", "shasum": "0e637ddae6b6217c6c7dca36c3fd232f25fb2495"}, "require": {"drupal/core": "^10.3 || ^11", "php": ">=8.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://drupal.org/u/valderama"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/BR0kEN", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://drupal.org/u/daneelcm"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/purushotam.rai"}], "description": "A simple client-side hierarchical select widget for taxonomy terms.", "homepage": "https://www.drupal.org/project/cshs", "keywords": ["client-side-select", "hierarchical-select", "module", "select", "taxonomy"], "support": {"source": "https://git.drupalcode.org/project/cshs", "issues": "https://www.drupal.org/project/issues/cshs"}}, {"name": "drupal/ctools", "version": "4.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/ctools.git", "reference": "4.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/ctools-4.1.0.zip", "reference": "4.1.0", "shasum": "69f5889cf557df9e55519390e6a95cfa31b67874"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-3.x": "3.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (EclipseGc)", "homepage": "https://www.drupal.org/u/eclipsegc", "role": "Maintainer"}, {"name": "<PERSON> (japerry)", "homepage": "https://www.drupal.org/u/japerry", "role": "Maintainer"}, {"name": "<PERSON> (tim.plunkett)", "homepage": "https://www.drupal.org/u/timplunkett", "role": "Maintainer"}, {"name": "<PERSON> (neclimdul)", "homepage": "https://www.drupal.org/u/neclimdul", "role": "Maintainer"}, {"name": "<PERSON> (da<PERSON>hn<PERSON>)", "homepage": "https://www.drupal.org/u/dawehner", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/26979"}, {"name": "neclimdul", "homepage": "https://www.drupal.org/user/48673"}, {"name": "sdboyer", "homepage": "https://www.drupal.org/user/146719"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "tim.plunkett", "homepage": "https://www.drupal.org/user/241634"}], "description": "Provides a number of utility and helper APIs for Drupal developers and site builders.", "homepage": "https://www.drupal.org/project/ctools", "support": {"source": "https://git.drupalcode.org/project/ctools", "issues": "https://www.drupal.org/project/issues/ctools"}}, {"name": "drupal/devel", "version": "5.3.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel.git", "reference": "5.3.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel-5.3.1.zip", "reference": "5.3.1", "shasum": "6a5f13bdf93dc5f7f194b6af847589ae15e37b63"}, "require": {"doctrine/common": "^2.7 || ^3.4", "drupal/core": "^10.3 || ^11 || ^12", "php": ">=8.1", "symfony/var-dumper": "^4 || ^5 || ^6 || ^7"}, "conflict": {"drupal/core": "<10.3", "drush/drush": "<12.5.1", "kint-php/kint": "<3"}, "require-dev": {"drush/drush": "^13", "firephp/firephp-core": "^0.5.3", "kint-php/kint": "^5.1"}, "suggest": {"kint-php/kint": "Kint provides an informative display of arrays/objects. Useful for debugging and developing."}, "type": "drupal-module", "extra": {"drupal": {"version": "5.3.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "moshe weitzman", "homepage": "https://www.drupal.org/user/23"}], "description": "Various blocks, pages, and functions for developers.", "homepage": "https://www.drupal.org/project/devel", "support": {"source": "https://gitlab.com/drupalspoons/devel", "issues": "https://gitlab.com/drupalspoons/devel/-/issues", "slack": "https://drupal.slack.com/archives/C012WAW1MH6"}}, {"name": "drupal/easy_breadcrumb", "version": "2.0.9", "source": {"type": "git", "url": "https://git.drupalcode.org/project/easy_breadcrumb.git", "reference": "2.0.9"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/easy_breadcrumb-2.0.9.zip", "reference": "2.0.9", "shasum": "9e7c33e2ec0637d37d509776795a476f2f2d9bb8"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.9", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/greg-boggs", "role": "Maintainer"}, {"name": "diamondsea", "homepage": "https://www.drupal.org/user/430714"}, {"name": "greg boggs", "homepage": "https://www.drupal.org/user/153069"}, {"name": "hmartens", "homepage": "https://www.drupal.org/user/622826"}, {"name": "loopduplicate", "homepage": "https://www.drupal.org/user/717290"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}, {"name": "nick<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3094661"}, {"name": "rakesh.gectcr", "homepage": "https://www.drupal.org/user/1177822"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1667988"}, {"name": "spuky", "homepage": "https://www.drupal.org/user/209353"}, {"name": "tatarbj", "homepage": "https://www.drupal.org/user/649590"}], "description": "Adds configuration to the system breadcrumbs.", "homepage": "https://www.drupal.org/project/easy_breadcrumb", "support": {"source": "https://git.drupalcode.org/project/easy_breadcrumb", "issues": "https://www.drupal.org/project/issues/easy_breadcrumb"}}, {"name": "drupal/editor_advanced_link", "version": "2.2.6", "source": {"type": "git", "url": "https://git.drupalcode.org/project/editor_advanced_link.git", "reference": "2.2.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/editor_advanced_link-2.2.6.zip", "reference": "2.2.6", "shasum": "f6d7c437900f288b1e735b4faf2decc99bdd30e8"}, "require": {"drupal/core": "^10.2 || ^11.0"}, "require-dev": {"drupal/ckeditor": "*", "phpro/grumphp": "^2.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/931394"}, {"name": "VladimirAus", "homepage": "https://www.drupal.org/user/673120"}], "description": "Editor Advanced link", "homepage": "https://www.drupal.org/project/editor_advanced_link", "support": {"source": "https://git.drupalcode.org/project/editor_advanced_link"}}, {"name": "drupal/entity_browser", "version": "2.13.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_browser.git", "reference": "8.x-2.13"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_browser-8.x-2.13.zip", "reference": "8.x-2.13", "shasum": "9c31caed2602a5cb582cb4f1e1feaac44cbbc21b"}, "require": {"drupal/core": "^10.2 || ^11"}, "conflict": {"drupal/media_entity": "1.*"}, "require-dev": {"drupal/embed": "^1.0", "drupal/entity_embed": "^1.0", "drupal/entity_reference_revisions": "^1.0", "drupal/entityqueue": "^1.0", "drupal/inline_entity_form": "^1 || ^3", "drupal/paragraphs": "^1.0", "drupal/search_api": "^1.0", "drupal/token": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.13", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/slashrsm", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/primsi", "role": "Maintainer"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/1943336/committers", "role": "contributor"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "marcingy", "homepage": "https://www.drupal.org/user/77320"}, {"name": "oknate", "homepage": "https://www.drupal.org/user/471638"}, {"name": "primsi", "homepage": "https://www.drupal.org/user/282629"}, {"name": "samuel.mortenson", "homepage": "https://www.drupal.org/user/2582268"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}], "description": "Entity browsing and selecting component.", "homepage": "https://drupal.org/project/entity_browser", "support": {"source": "https://git.drupalcode.org/project/entity_browser", "issues": "https://www.drupal.org/project/issues/entity_browser", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/entity_reference_revisions", "version": "1.12.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_reference_revisions.git", "reference": "8.x-1.12"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_reference_revisions-8.x-1.12.zip", "reference": "8.x-1.12", "shasum": "2a2ff8617c7ce01b56df1caaf0a563da04948e26"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/diff": "^1 || ^2"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.12", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}], "description": "Entity Reference Revisions", "homepage": "https://www.drupal.org/project/entity_reference_revisions", "support": {"source": "https://git.drupalcode.org/project/entity_reference_revisions"}}, {"name": "drupal/entity_reference_unpublished", "version": "2.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_reference_unpublished.git", "reference": "2.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_reference_unpublished-2.0.1.zip", "reference": "2.0.1", "shasum": "ed129fdb018ca1a2e29076f10fc5994ccef6d82e"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3567222"}, {"name": "lahoosascoots", "homepage": "https://www.drupal.org/user/1933614"}], "description": "Allows unpublished content to be referenced in an entity reference.", "homepage": "https://www.drupal.org/project/entity_reference_unpublished", "support": {"source": "https://git.drupalcode.org/project/entity_reference_unpublished"}}, {"name": "drupal/entity_share", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_share.git", "reference": "8.x-3.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_share-8.x-3.0.zip", "reference": "8.x-3.0", "shasum": "b1c8f40ef2714c45c956c09343e9e9808d3e8a10"}, "require": {"drupal/core": "^9 || ^10", "league/oauth2-client": "^2.4"}, "conflict": {"drupal/jsonapi": "1.*"}, "require-dev": {"drupal/block_field": "~1.0", "drupal/dynamic_entity_reference": "~3.0 || ~4.0", "drupal/entity_share_client": "*", "drupal/field_group": "~3.0", "drupal/json_api_book": "~1.0", "drupal/jsonapi_extras": "~3.14", "drupal/key": "~1.14", "drupal/metatag": "~1.0", "drupal/paragraphs": "~1.0", "drupal/pathauto": "~1.0"}, "suggest": {"drupal/key": "Provides ability to manage keys for Authentication plugins.", "drupal/simple_oauth": "Allows to setup server for OAuth plugin."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "grimreaper", "homepage": "https://www.drupal.org/user/2388214"}, {"name": "ithom", "homepage": "https://www.drupal.org/user/3175403"}, {"name": "i<PERSON><PERSON>v<PERSON>", "homepage": "https://www.drupal.org/user/382945"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "yarik.lutsiuk", "homepage": "https://www.drupal.org/user/3212333"}], "description": "Provides an UI to share entities.", "homepage": "https://www.drupal.org/project/entity_share", "support": {"source": "https://git.drupalcode.org/project/entity_share"}}, {"name": "drupal/entity_usage", "version": "2.0.0-beta24", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_usage.git", "reference": "8.x-2.0-beta24"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_usage-8.x-2.0-beta24.zip", "reference": "8.x-2.0-beta24", "shasum": "063cf50d2b5cf7c99bb86a818c03fcfef2082151"}, "require": {"drupal/core": "^10.2 || ^11"}, "require-dev": {"drupal/block_field": "~1.0", "drupal/dynamic_entity_reference": "~1.0 || ^2.0 || ^4.0", "drupal/entity_browser": "~2.0", "drupal/entity_browser_block": "~1.0 || ^2.0", "drupal/entity_embed": "^1.7", "drupal/entity_reference_revisions": "~1.0", "drupal/inline_entity_form": "^1.0@RC || ^3.0@RC", "drupal/paragraphs": "~1.0", "drupal/redirect": "^1.11", "drupal/webform": "^6.0.0-alpha4"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.0-beta24", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}, "drush": {"services": {"drush.services.yml": "^9 || ^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "lull<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3815489"}, {"name": "marc<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1288796"}, {"name": "seanb", "homepage": "https://www.drupal.org/user/545912"}], "description": "Track usage of entities referenced by other entities", "homepage": "https://www.drupal.org/project/entity_usage", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/entity_usage", "issues": "http://drupal.org/project/issues/entity_usage"}}, {"name": "drupal/field_group", "version": "3.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/field_group.git", "reference": "8.x-3.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/field_group-8.x-3.6.zip", "reference": "8.x-3.6", "shasum": "427c0a65dc1936e69e60c120776056cfe5b43e86"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "require-dev": {"drupal/jquery_ui_accordion": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "hydra", "homepage": "https://www.drupal.org/user/647364"}, {"name": "joevagyok", "homepage": "https://www.drupal.org/user/2876343"}, {"name": "jyve", "homepage": "https://www.drupal.org/user/591438"}, {"name": "nils.destoop", "homepage": "https://www.drupal.org/user/361625"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/322618"}, {"name": "swentel", "homepage": "https://www.drupal.org/user/107403"}], "description": "Provides the field_group module.", "homepage": "https://www.drupal.org/project/field_group", "support": {"source": "https://git.drupalcode.org/project/field_group", "issues": "https://www.drupal.org/project/issues/field_group"}}, {"name": "drupal/focal_point", "version": "2.1.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/focal_point.git", "reference": "2.1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/focal_point-2.1.2.zip", "reference": "2.1.2", "shasum": "5f8ffadd37748506c8f00314b1d45c947eb27cf7"}, "require": {"drupal/core": "^9.3 || ^10 || ^11", "drupal/crop": "^2.3"}, "require-dev": {"drupal/crop": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (bleen)", "homepage": "https://www.drupal.org/u/bleen", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3418561"}], "description": "Focal Point allows content creators to mark the most important part of an image for easier cropping.", "homepage": "https://drupal.org/project/focal_point", "support": {"source": "https://cgit.drupalcode.org/focal_point", "issues": "https://drupal.org/project/issues/focal_point", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/gin", "version": "4.0.6", "source": {"type": "git", "url": "https://git.drupalcode.org/project/gin.git", "reference": "4.0.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/gin-4.0.6.zip", "reference": "4.0.6", "shasum": "c68a0fb646c439e1eea3f31f282e59be36eadab2"}, "require": {"drupal/core": "^10 || ^11", "drupal/gin_toolbar": "^2.0"}, "type": "drupal-theme", "extra": {"drupal": {"version": "4.0.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "scripts": {"phpcs": ["phpcs -s --runtime-set ignore_warnings_on_exit 1 --runtime-set ignore_errors_on_exit 0 'web/modules/custom'"]}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (saschaeggi)", "homepage": "https://www.drupal.org/u/saschaeggi", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1999056"}], "description": "For a better Admin and Content Editor Experience.", "homepage": "https://www.drupal.org/project/gin", "support": {"source": "https://git.drupalcode.org/project/gin", "issues": "https://www.drupal.org/project/issues/gin"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/saschaeggi"}, {"type": "other", "url": "https://paypal.me/saschaeggi"}]}, {"name": "drupal/gin_login", "version": "2.1.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/gin_login.git", "reference": "2.1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/gin_login-2.1.3.zip", "reference": "2.1.3", "shasum": "4fd1a4f36205f511ab7c222f3543aa15ad2331c6"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON> (saschaeggi)", "homepage": "https://www.drupal.org/u/saschaeggi", "role": "Maintainer"}], "description": "Custom <PERSON><PERSON><PERSON> Login for Gin theme", "homepage": "https://www.drupal.org/project/gin_login", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/gin_login", "issues": "https://www.drupal.org/project/issues/gin_login"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/saschaeggi"}, {"type": "other", "url": "https://paypal.me/saschaeggi"}]}, {"name": "drupal/gin_toolbar", "version": "2.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/gin_toolbar.git", "reference": "2.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/gin_toolbar-2.0.0.zip", "reference": "2.0.0", "shasum": "2befeab2de9f7953b76b1a36c9bfb6a7e3987b11"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON> (saschaeggi)", "homepage": "https://www.drupal.org/u/saschaeggi", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1999056"}], "description": "Gin Toolbar for Frontend use", "homepage": "https://www.drupal.org/project/gin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/gin_toolbar", "issues": "https://www.drupal.org/project/issues/gin_toolbar"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/saschaeggi"}, {"type": "other", "url": "https://paypal.me/saschaeggi"}]}, {"name": "drupal/honeypot", "version": "2.2.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/honeypot.git", "reference": "2.2.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/honeypot-2.2.2.zip", "reference": "2.2.2", "shasum": "828872d31d1a2c37a818cacae7fcd77a60996c66"}, "require": {"drupal/core": "^10.3 || ^11"}, "require-dev": {"drupal/rules": "^4.0", "drupal/webform": "^6.2"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/389011", "email": "<EMAIL>"}, {"name": "man<PERSON> garcia", "homepage": "https://www.drupal.org/user/213194"}, {"name": "tr", "homepage": "https://www.drupal.org/user/202830"}, {"name": "vijaycs85", "homepage": "https://www.drupal.org/user/93488"}], "description": "Mitigates spam form submissions using the honeypot method.", "homepage": "https://www.drupal.org/project/honeypot", "keywords": ["deterrent", "form", "honeypot", "honeytrap", "php", "spam"], "support": {"source": "https://git.drupalcode.org/project/honeypot", "issues": "https://www.drupal.org/project/issues/honeypot"}}, {"name": "drupal/icon_select", "version": "3.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/icon_select.git", "reference": "3.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/icon_select-3.0.1.zip", "reference": "3.0.1", "shasum": "858a321d80d88bede07d57a1306ca170ba15d454"}, "require": {"drupal/core": "^10.1 || ^11", "enshrined/svg-sanitize": ">=0.9 <1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "ayalon", "homepage": "https://www.drupal.org/user/419226"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1146660"}, {"name": "gperriard", "homepage": "https://www.drupal.org/user/3362098"}, {"name": "make77", "homepage": "https://www.drupal.org/user/858788"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3634706"}], "description": "Icon selector", "homepage": "https://www.drupal.org/project/icon_select", "support": {"source": "https://git.drupalcode.org/project/icon_select"}}, {"name": "drupal/jsonapi_extras", "version": "3.26.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jsonapi_extras.git", "reference": "8.x-3.26"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jsonapi_extras-8.x-3.26.zip", "reference": "8.x-3.26", "shasum": "344fe5580f27ef4527ddb3e2eb8ef73bdf34bd82"}, "require": {"drupal/core": "^9.5 || ^10 || ^11", "e0ipso/shaper": "^1", "php": ">=8.1"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.26", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3366066", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mkolar"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/karlos007"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/bbrala"}], "description": "JSON:API Extras provides a means to override and provide limited configurations to the default zero-configuration implementation provided by the JSON:API module.", "homepage": "https://www.drupal.org/project/jsonapi_extras", "support": {"source": "https://git.drupalcode.org/project/jsonapi_extras"}}, {"name": "drupal/link_class", "version": "2.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/link_class.git", "reference": "2.2.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/link_class-2.2.0.zip", "reference": "2.2.0", "shasum": "eae90fae6779e63242833f40a31c9ea6bcd30aee"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "flocondetoile", "homepage": "https://www.drupal.org/user/2006064"}], "description": "Provide a widget to Link field type for adding class to link.", "homepage": "https://www.drupal.org/project/link_class", "support": {"source": "https://git.drupalcode.org/project/link_class"}}, {"name": "drupal/link_field_autocomplete_filter", "version": "2.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/link_field_autocomplete_filter.git", "reference": "2.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/link_field_autocomplete_filter-2.0.5.zip", "reference": "2.0.5", "shasum": "c8d95b2cd164516a65d865e371147a5ec4f60df8"}, "require": {"drupal/core": "^9.1 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "jmuxf", "homepage": "https://www.drupal.org/user/1275704"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1885838"}], "description": "Link Field Autocomplete Filter", "homepage": "https://www.drupal.org/project/link_field_autocomplete_filter", "keywords": ["<PERSON><PERSON><PERSON>", "Linkfield"], "support": {"source": "https://git.drupalcode.org/project/link_field_autocomplete_filter"}}, {"name": "drupal/link_target", "version": "1.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/link_target.git", "reference": "8.x-1.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/link_target-8.x-1.5.zip", "reference": "8.x-1.5", "shasum": "5dc4cf30e32613e94741a6e3e760bd2fbe125c2b"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "homepage": "https://www.drupal.org/user/3267792"}, {"name": "mandclu", "homepage": "https://www.drupal.org/user/52136"}, {"name": "mturner20", "homepage": "https://www.drupal.org/user/3520303"}], "description": "Provide a widget for Link field type which permits to choose target.", "homepage": "https://www.drupal.org/project/link_target", "support": {"source": "https://git.drupalcode.org/project/link_target"}}, {"name": "drupal/linkit", "version": "7.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/linkit.git", "reference": "7.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/linkit-7.0.3.zip", "reference": "7.0.3", "shasum": "bc880d9fbc08a1cbd1ee76fab845841ec79ce4e7"}, "require": {"drupal/core": "^10.1 || ^11"}, "require-dev": {"drupal/ckeditor": "*", "drupal/imce": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "7.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://stjerneman.com", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "john<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3331569"}, {"name": "mark_fullmer", "homepage": "https://www.drupal.org/user/2612816"}], "description": "Linkit - Enriched linking experience", "homepage": "http://drupal.org/project/linkit", "support": {"source": "http://cgit.drupalcode.org/linkit", "issues": "http://drupal.org/project/linkit"}}, {"name": "drupal/mailsystem", "version": "4.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/mailsystem.git", "reference": "8.x-4.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/mailsystem-8.x-4.5.zip", "reference": "8.x-4.5", "shasum": "e52a814a87b343ab69f8d8ef462a9873c1d01158"}, "require": {"drupal/core": "^9 || ^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-4.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3225331"}, {"name": "jose<PERSON>.o<PERSON>", "homepage": "https://www.drupal.org/user/1321830"}, {"name": "les lim", "homepage": "https://www.drupal.org/user/84263"}, {"name": "man<PERSON> garcia", "homepage": "https://www.drupal.org/user/213194"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2489926"}, {"name": "pillarsdotnet", "homepage": "https://www.drupal.org/user/36148"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}, {"name": "tr", "homepage": "https://www.drupal.org/user/202830"}], "description": "Mail System", "homepage": "https://www.drupal.org/project/mailsystem", "support": {"source": "https://git.drupalcode.org/project/mailsystem"}}, {"name": "drupal/menu_link_attributes", "version": "1.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_link_attributes.git", "reference": "8.x-1.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_link_attributes-8.x-1.5.zip", "reference": "8.x-1.5", "shasum": "ad1db596ad8a76ba95fbf2d0a42d4777056587d5"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "jcnventura", "homepage": "https://www.drupal.org/user/122464"}, {"name": "yannickoo", "homepage": "https://www.drupal.org/user/531118"}], "description": "Allows you to add attributes to menu links.", "homepage": "http://drupal.org/project/menu_link_attributes", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/menu_link_attributes", "issues": "http://drupal.org/project/issues/menu_link_attributes"}}, {"name": "drupal/metatag", "version": "2.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/metatag.git", "reference": "2.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/metatag-2.1.0.zip", "reference": "2.1.0", "shasum": "c28fe2fdac68a9370a6af6cbafff4425dd5148f3"}, "require": {"drupal/core": "^9.4 || ^10 || ^11", "drupal/token": "^1.0", "php": ">=8.0"}, "require-dev": {"drupal/forum": "*", "drupal/hal": "^1 || ^2 || ^9", "drupal/metatag_dc": "*", "drupal/metatag_open_graph": "*", "drupal/page_manager": "^4.0", "drupal/redirect": "^1.0", "ergebnis/composer-normalize": "*", "mpyw/phpunit-patch-serializable-comparison": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "composer-normalize": {"indent-size": 2, "indent-style": "space"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "See contributors", "homepage": "https://www.drupal.org/node/640498/committers", "role": "Developer"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}], "description": "Manage meta tags for all entities.", "homepage": "https://www.drupal.org/project/metatag", "keywords": ["<PERSON><PERSON><PERSON>", "seo"], "support": {"source": "https://git.drupalcode.org/project/metatag", "issues": "https://www.drupal.org/project/issues/metatag", "docs": "https://www.drupal.org/docs/8/modules/metatag"}}, {"name": "drupal/metatag_import_export_csv", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/metatag_import_export_csv.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/metatag_import_export_csv-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "544b20b728ba7380a8b7423a89d5cf484da66541"}, "require": {"drupal/core": "^9.3 || ^10 || ^11", "drupal/metatag": "*", "drupal/token": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "aayush23", "homepage": "https://www.drupal.org/user/3389476"}, {"name": "DamienMcKenna", "homepage": "https://www.drupal.org/user/108450"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "namit.garg", "homepage": "https://www.drupal.org/user/3258680"}], "description": "Import or Export Metatags with the help of a CSV file", "homepage": "https://www.drupal.org/project/metatag_import_export_csv", "support": {"source": "https://git.drupalcode.org/project/metatag_import_export_csv"}}, {"name": "drupal/migrate_file_to_media", "version": "2.0.14", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_file_to_media.git", "reference": "2.0.14"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_file_to_media-2.0.14.zip", "reference": "2.0.14", "shasum": "33519d0706dc71582c62fe044fce80fe6ddf46d4"}, "require": {"drupal/core": "^10.2 || ^11", "drupal/migrate_plus": "^6", "drupal/migrate_tools": "^6", "php": ">=8.1.0"}, "require-dev": {"drupal/crop": "^2.0", "drupal/paragraphs": "^1.10"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.14", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (ayalon)", "homepage": "https://www.drupal.org/u/ayalon", "role": "Maintainer"}, {"name": "hitchshock", "homepage": "https://www.drupal.org/user/3552980"}], "description": "Migrate file fields to media fields.", "homepage": "https://drupal.org/project/migrate_file_to_media", "support": {"source": "https://git.drupalcode.org/project/migrate_file_to_media", "issues": "https://www.drupal.org/project/issues/migrate_file_to_media"}}, {"name": "drupal/migrate_plus", "version": "6.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_plus.git", "reference": "6.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_plus-6.0.5.zip", "reference": "6.0.5", "shasum": "441e91086feaca7a6f1acf1735023fff0dfc5e1e"}, "require": {"drupal/core": ">=9.1", "php": ">=7.4"}, "require-dev": {"drupal/migrate_example_advanced_setup": "*", "drupal/migrate_example_setup": "*"}, "suggest": {"ext-soap": "*", "sainsburys/guzzle-oauth2-plugin": "3.0 required for the OAuth2 authentication plugin"}, "type": "drupal-module", "extra": {"drupal": {"version": "6.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Enhancements to core migration support.", "homepage": "https://www.drupal.org/project/migrate_plus", "support": {"source": "https://git.drupalcode.org/project/migrate_plus", "issues": "https://www.drupal.org/project/issues/migrate_plus", "slack": "#migrate"}}, {"name": "drupal/migrate_source_csv", "version": "3.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_source_csv.git", "reference": "8.x-3.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_source_csv-8.x-3.7.zip", "reference": "8.x-3.7", "shasum": "d952a58003a4616176050ce4d486f9df9c6c59c9"}, "require": {"drupal/core": ">=9.1", "league/csv": "^9.1", "php": ">=7.1"}, "require-dev": {"drupal/migrate_plus": ">=5.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}], "description": "CSV source migration.", "homepage": "https://www.drupal.org/project/migrate_source_csv", "support": {"source": "https://cgit.drupalcode.org/migrate_source_csv", "issues": "https://www.drupal.org/project/issues/migrate_source_csv"}}, {"name": "drupal/migrate_tools", "version": "6.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_tools.git", "reference": "6.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_tools-6.0.5.zip", "reference": "6.0.5", "shasum": "c82519b366f43827818b04bfbc0009a6e028b835"}, "require": {"drupal/core": ">=9.1", "php": ">=7.4"}, "require-dev": {"drupal/migrate_plus": ">=5", "drupal/migrate_source_csv": ">=3", "drush/drush": ">=11"}, "suggest": {"drupal/migrate_plus": ">=5", "drush/drush": ">=11"}, "type": "drupal-module", "extra": {"drupal": {"version": "6.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Tools to assist in developing and running migrations.", "homepage": "http://drupal.org/project/migrate_tools", "support": {"source": "https://git.drupalcode.org/project/migrate_tools", "issues": "https://www.drupal.org/project/issues/migrate_tools", "slack": "#migrate"}}, {"name": "drupal/mimemail", "version": "1.0.0-alpha6", "source": {"type": "git", "url": "https://git.drupalcode.org/project/mimemail.git", "reference": "8.x-1.0-alpha6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/mimemail-8.x-1.0-alpha6.zip", "reference": "8.x-1.0-alpha6", "shasum": "c25246747dac4372c7d5a5a5fd0f276d9e468eff"}, "require": {"drupal/core": "^9.3 || ^10", "drupal/mailsystem": "^4"}, "require-dev": {"drupal/rules": "^3"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-alpha6", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Alpha releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "tr", "homepage": "https://www.drupal.org/user/202830"}], "description": "Sends MIME-encoded emails with embedded images and attachments.", "homepage": "https://www.drupal.org/project/mimemail", "support": {"source": "https://git.drupalcode.org/project/mimemail", "issues": "https://www.drupal.org/project/issues/mimemail"}}, {"name": "drupal/minifyhtml", "version": "2.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/minifyhtml.git", "reference": "2.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/minifyhtml-2.0.5.zip", "reference": "2.0.5", "shasum": "e400b98a45257ce0443e17f4e139b94fbea54f93"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/coder": "^8.3", "drupal/core-dev": "^10", "squizlabs/php_codesniffer": "^3.7"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/1846786", "email": "<EMAIL>", "role": "maintainer"}], "description": "Minify the contents of the <PERSON><PERSON><PERSON> page", "homepage": "https://www.drupal.org/project/minifyhtml", "support": {"source": "https://git.drupalcode.org/project/minifyhtml", "issues": "https://www.drupal.org/project/issues/minifyhtml"}}, {"name": "drupal/node_revision_delete", "version": "2.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/node_revision_delete.git", "reference": "2.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/node_revision_delete-2.0.0.zip", "reference": "2.0.0", "shasum": "3ab4174378475a22a0dab5fc77939bb75fecdfa8"}, "require": {"drupal/core": "^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (adriancid)", "homepage": "https://www.drupal.org/u/adriancid", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (diosbelmezquia)", "homepage": "https://www.drupal.org/u/diosbelmezquia", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (<PERSON>)", "homepage": "https://www.drupal.org/u/robert-ngo", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (seanB)", "homepage": "https://www.drupal.org/u/seanB", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (Rajeshreeputra)", "homepage": "https://www.drupal.org/u/raj<PERSON><PERSON>putra", "role": "Maintainer"}], "description": "Track and prune node revisions.", "homepage": "https://www.drupal.org/project/node_revision_delete", "keywords": ["<PERSON><PERSON><PERSON>", "Nodes", "Revisions"], "support": {"source": "http://cgit.drupalcode.org/node_revision_delete", "issues": "https://www.drupal.org/project/issues/node_revision_delete"}}, {"name": "drupal/paragraphs", "version": "1.19.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs.git", "reference": "8.x-1.19"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs-8.x-1.19.zip", "reference": "8.x-1.19", "shasum": "831a81a11eac419e8410db45efef5b283c4d117c"}, "require": {"drupal/core": "^10.2 || ^11", "drupal/entity_reference_revisions": "~1.3"}, "require-dev": {"drupal/block_field": "1.x-dev", "drupal/diff": "1.x-dev", "drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/feeds": "^3", "drupal/field_group": "3.x-dev", "drupal/inline_entity_form": "3.x-dev", "drupal/paragraphs-paragraphs_library": "*", "drupal/replicate": "1.x-dev", "drupal/search_api": "^1", "drupal/search_api_db": "*"}, "suggest": {"drupal/entity_browser": "Recommended for an improved user experience when using the Paragraphs library module"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.19", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "frans", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "j<PERSON>ller", "homepage": "https://www.drupal.org/user/99012"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}, {"name": "primsi", "homepage": "https://www.drupal.org/user/282629"}], "description": "Enables the creation of Paragraphs entities.", "homepage": "https://www.drupal.org/project/paragraphs", "support": {"source": "https://git.drupalcode.org/project/paragraphs"}}, {"name": "drupal/paragraphs_edit", "version": "3.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs_edit.git", "reference": "3.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs_edit-3.0.1.zip", "reference": "3.0.1", "shasum": "d9c66cee07e93ca0c209651f569705f274bee0de"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11", "drupal/paragraphs": "*"}, "require-dev": {"drupal/paragraphs": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "astonvictor", "homepage": "https://www.drupal.org/user/3466615"}, {"name": "b<PERSON>la", "homepage": "https://www.drupal.org/user/3366066"}, {"name": "casey", "homepage": "https://www.drupal.org/user/32403"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3558376"}], "description": "Allows users to edit/clone and delete paragraphs.", "homepage": "https://www.drupal.org/project/paragraphs_edit", "support": {"source": "https://git.drupalcode.org/project/paragraphs_edit"}}, {"name": "drupal/path_redirect_import", "version": "2.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/path_redirect_import.git", "reference": "2.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/path_redirect_import-2.1.0.zip", "reference": "2.1.0", "shasum": "31748990ba3a074eb40e738d5bddbad522dc1dd3"}, "require": {"drupal/core": ">=9.1", "drupal/migrate_source_csv": "^3.5", "drupal/migrate_tools": "^6.0.2", "drupal/redirect": ">=1.7", "php": ">=7.1"}, "require-dev": {"drupal/migrate_plus": ">=5.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/plopesc", "role": "Maintainer"}, {"name": "paul<PERSON>s", "homepage": "https://www.drupal.org/user/3640109"}, {"name": "plopesc", "homepage": "https://www.drupal.org/user/282415"}], "description": "Migrate Drupal redirects from CSV.", "homepage": "https://www.drupal.org/project/path_redirect_import", "support": {"source": "https://git.drupalcode.org/path_redirect_import", "issues": "https://www.drupal.org/project/issues/path_redirect_import"}}, {"name": "drupal/pathauto", "version": "1.13.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/pathauto.git", "reference": "8.x-1.13"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/pathauto-8.x-1.13.zip", "reference": "8.x-1.13", "shasum": "e64b5a82cf1b8ab48bce400b21ae6fc99c8078fd"}, "require": {"drupal/core": "^9.4 || ^10 || ^11", "drupal/ctools": "*", "drupal/token": "*"}, "require-dev": {"drupal/forum": "*"}, "suggest": {"drupal/redirect": "When installed Pathauto will provide a new \"Update Action\" in case your URLs change. This is the recommended update action and is considered the best practice for SEO and usability."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.13", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "Freso", "homepage": "https://www.drupal.org/user/27504"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}], "description": "Provides a mechanism for modules to automatically generate aliases for the content they manage.", "homepage": "https://www.drupal.org/project/pathauto", "support": {"source": "https://cgit.drupalcode.org/pathauto", "issues": "https://www.drupal.org/project/issues/pathauto", "documentation": "https://www.drupal.org/docs/8/modules/pathauto"}}, {"name": "drupal/quick_node_clone", "version": "1.22.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/quick_node_clone.git", "reference": "8.x-1.22"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/quick_node_clone-8.x-1.22.zip", "reference": "8.x-1.22", "shasum": "39f3c80627803c0e1f5d342327b17c16a691cdec"}, "require": {"drupal/core": "^10 || ^11"}, "require-dev": {"drupal/group": "^1 || ^2 || ^3", "drupal/paragraphs": "1.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.22", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "vilepickle", "homepage": "https://www.drupal.org/u/vilepickle", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto", "role": "Maintainer"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}], "description": "Quickly clone a node with regular fields.", "homepage": "https://www.drupal.org/project/quick_node_clone", "support": {"source": "https://git.drupalcode.org/project/quick_node_clone", "issues": "https://www.drupal.org/project/issues/quick_node_clone"}}, {"name": "drupal/rebuild_cache_access", "version": "1.12.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/rebuild_cache_access.git", "reference": "8.x-1.12"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/rebuild_cache_access-8.x-1.12.zip", "reference": "8.x-1.12", "shasum": "1c6bff8f2ddb4cf943b986ac40932c63c1bd749c"}, "require": {"drupal/core": "^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.12", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "leymannx", "homepage": "https://www.drupal.org/user/2482808"}], "description": "Provide a Rebuild Cache admin toolbar button accessible to every role with the corresponding permission set.", "homepage": "https://www.drupal.org/project/rebuild_cache_access", "support": {"source": "https://git.drupalcode.org/project/rebuild_cache_access"}}, {"name": "drupal/recaptcha_v3", "version": "2.0.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/recaptcha_v3.git", "reference": "2.0.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/recaptcha_v3-2.0.4.zip", "reference": "2.0.4", "shasum": "f108d19d1c14dac0ea2b16b1cc30f6066f3c00c2"}, "require": {"drupal/captcha": "^2.0", "drupal/core": "^10 || ^11", "google/recaptcha": "^1.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "b-prod", "homepage": "https://www.drupal.org/user/407852"}, {"name": "dench0", "homepage": "https://www.drupal.org/user/896504"}, {"name": "majid.ali", "homepage": "https://www.drupal.org/user/1271330"}], "description": "The reCaptcha V3 module provides integration with Google reCaptcha V3 and CAPTCHA module.", "homepage": "https://www.drupal.org/project/recaptcha_v3", "support": {"source": "https://git.drupalcode.org/project/recaptcha_v3"}}, {"name": "drupal/redirect", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redirect.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redirect-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "7df8b3524bbde07d254216039636947a689140ef"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "kristen pol", "homepage": "https://www.drupal.org/user/8389"}, {"name": "pifagor", "homepage": "https://www.drupal.org/user/2375692"}], "description": "Allows users to redirect from old URLs to new URLs.", "homepage": "https://www.drupal.org/project/redirect", "support": {"source": "https://git.drupalcode.org/project/redirect"}}, {"name": "drupal/responsive_preview", "version": "2.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/responsive_preview.git", "reference": "2.2.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/responsive_preview-2.2.0.zip", "reference": "2.2.0", "shasum": "4d4ce0dde333e9bd1b2d70c0e23cf75a009be830"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chr.fritsch", "homepage": "https://www.drupal.org/user/2103716"}, {"name": "eatings", "homepage": "https://www.drupal.org/user/105524"}, {"name": "j<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/748566"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3418561"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1043862"}], "description": "Provides a component that previews a page in various device dimensions.", "homepage": "https://www.drupal.org/project/responsive_preview", "support": {"source": "https://git.drupalcode.org/project/responsive_preview"}}, {"name": "drupal/restui", "version": "1.22.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/restui.git", "reference": "8.x-1.22"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/restui-8.x-1.22.zip", "reference": "8.x-1.22", "shasum": "7c9fb14c574f8a4090b77b1bcbc5be141409a383"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.22", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "-enzo-", "homepage": "https://www.drupal.org/user/294937"}, {"name": "clemens.tolboom", "homepage": "https://www.drupal.org/user/125814"}, {"name": "jua<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/682736"}, {"name": "kamkejj", "homepage": "https://www.drupal.org/user/81043"}, {"name": "vipin.mittal18", "homepage": "https://www.drupal.org/user/319716"}], "description": "Provides a user interface to manage REST resources.", "homepage": "https://www.drupal.org/project/restui", "support": {"source": "https://git.drupalcode.org/project/restui"}}, {"name": "drupal/scheduler", "version": "2.2.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/scheduler.git", "reference": "2.2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/scheduler-2.2.1.zip", "reference": "2.2.1", "shasum": "ce9a9405ea88140fbdd53f1e3ed3741b6f27266c"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "require-dev": {"drupal/commerce": "^2 || ^3", "drupal/devel_generate": ">=4", "drupal/rules": "^3 || ^4", "drupal/workbench_moderation": "*", "drupal/workbench_moderation_actions": "*", "drush/drush": ">=9"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (<PERSON>)", "homepage": "https://www.drupal.org/u/eric-schaefer", "role": "Maintainer"}, {"name": "<PERSON> (jonathan1055)", "homepage": "https://www.drupal.org/u/jonathan1055", "role": "Maintainer"}, {"name": "<PERSON> (pfrenssen)", "homepage": "https://www.drupal.org/u/pfrenssen", "role": "Maintainer"}, {"name": "<PERSON> (rickmanelius)", "homepage": "https://www.drupal.org/u/rickmanelius", "role": "Maintainer"}], "description": "Automatically publish and unpublish content at specified dates and times.", "homepage": "https://drupal.org/project/scheduler", "support": {"source": "https://git.drupalcode.org/project/scheduler", "issues": "https://www.drupal.org/project/issues/scheduler"}}, {"name": "drupal/scheduler_content_moderation_integration", "version": "3.0.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/scheduler_content_moderation_integration.git", "reference": "3.0.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/scheduler_content_moderation_integration-3.0.4.zip", "reference": "3.0.4", "shasum": "07b57e1817c01902a5709cdcbd4578e65622ce53"}, "require": {"drupal/core": "^10.3 || ^11", "drupal/scheduler": "^2.1"}, "require-dev": {"drupal/commerce": "^3.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chr.fritsch", "homepage": "https://www.drupal.org/user/2103716"}, {"name": "daniel.bosen", "homepage": "https://www.drupal.org/user/404865"}, {"name": "jonathan1055", "homepage": "https://www.drupal.org/user/92645"}, {"name": "s<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3252890"}, {"name": "volkerk", "homepage": "https://www.drupal.org/user/57527"}], "description": "Scheduler sub-module providing content moderation functionality for publishing/unpublishing", "homepage": "https://www.drupal.org/project/scheduler_content_moderation_integration", "support": {"source": "https://git.drupalcode.org/project/scheduler_content_moderation_integration", "error": "Invalid dependency: \"scheduler\", Could not parse version constraint ~: Invalid version string \"~\""}}, {"name": "drupal/search_api", "version": "1.38.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api.git", "reference": "8.x-1.38"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api-8.x-1.38.zip", "reference": "8.x-1.38", "shasum": "d1c83ba74e553eca07d3ea4b15e5d9c7f009a496"}, "require": {"drupal/core": "^10.2 || ^11"}, "conflict": {"drupal/search_api_solr": "2.* || 3.0 || 3.1"}, "require-dev": {"drupal/language_fallback_fix": "@dev", "drupal/search_api_autocomplete": "@dev", "drupal/search_api_db": "*"}, "suggest": {"drupal/facets": "Adds the ability to create faceted searches.", "drupal/search_api_autocomplete": "Allows adding autocomplete suggestions to search fields.", "drupal/search_api_solr": "Adds support for using Apache Solr as a backend."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.38", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/drunken-monkey"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/nick_vh"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/790418/committers"}], "description": "Provides a generic framework for modules offering search capabilities.", "homepage": "https://www.drupal.org/project/search_api", "support": {"source": "https://git.drupalcode.org/project/search_api", "issues": "https://www.drupal.org/project/issues/search_api", "irc": "irc://irc.freenode.org/drupal-search-api"}}, {"name": "drupal/search_api_autocomplete", "version": "1.10.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api_autocomplete.git", "reference": "8.x-1.10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api_autocomplete-8.x-1.10.zip", "reference": "8.x-1.10", "shasum": "bd7d4a35a595efb866f6f5e2230d62d93b074927"}, "require": {"drupal/core": "^10.2 || ^11", "drupal/search_api": "^1.0"}, "require-dev": {"drupal/search_api_page": "1.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.10", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/drunken-monkey"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/1142202/committers"}], "description": "Adds autocomplete functionality to searches.", "homepage": "https://www.drupal.org/project/search_api_autocomplete", "support": {"source": "http://git.drupal.org/project/search_api_autocomplete.git", "issues": "https://www.drupal.org/project/issues/search_api_autocomplete", "irc": "irc://irc.freenode.org/drupal-search-api"}}, {"name": "drupal/search_api_exclude", "version": "2.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api_exclude.git", "reference": "2.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api_exclude-2.0.3.zip", "reference": "2.0.3", "shasum": "e3a4bebe7be44417ad87e3a463678f89b5fdad0b"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11", "drupal/search_api": "^1.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/317279"}, {"name": "MiSc", "homepage": "https://www.drupal.org/user/382892"}, {"name": "stijnstroobants", "homepage": "https://www.drupal.org/user/1787140"}], "description": "The Search API Exclude module allows users to prevent certain nodes from being indexed.", "homepage": "https://www.drupal.org/project/search_api_exclude", "support": {"source": "https://git.drupalcode.org/project/search_api_exclude", "issues": "https://www.drupal.org/project/issues/search_api_exclude"}}, {"name": "drupal/site_settings", "version": "2.0.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/site_settings.git", "reference": "2.0.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/site_settings-2.0.2.zip", "reference": "2.0.2", "shasum": "df5a4e1ae51b0ab9709d3dd86cb27e7e34ff44e8"}, "require": {"drupal/core": "^10 || ^11"}, "require-dev": {"drupal/token": "1.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "bobi-mel", "homepage": "https://www.drupal.org/user/3741631"}, {"name": "bohart", "homepage": "https://www.drupal.org/user/289861"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3567222"}, {"name": "scott_euser", "homepage": "https://www.drupal.org/user/3267594"}], "description": "Provides a site settings entity", "homepage": "https://www.drupal.org/project/site_settings", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/site_settings", "issues": "http://drupal.org/project/issues/site_settings"}}, {"name": "drupal/svg_image", "version": "3.2.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/svg_image.git", "reference": "3.2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/svg_image-3.2.1.zip", "reference": "3.2.1", "shasum": "4623b9d0de4c624857df10daaa8c68793942ad87"}, "require": {"drupal/core": "^10.3 || ^11", "enshrined/svg-sanitize": ">=0.15 <1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/imyaro", "role": "Maintainer"}, {"name": "See contributors", "homepage": "https://www.drupal.org/node/2887125/committers"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2870933"}, {"name": "mably", "homepage": "https://www.drupal.org/user/3375160"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "Overrides the standard image formatter and widget to support SVG files.", "homepage": "https://drupal.org/project/svg_image", "support": {"source": "https://git.drupalcode.org/project/svg_image", "issues": "https://www.drupal.org/project/issues/svg_image"}}, {"name": "drupal/textfield_counter", "version": "2.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/textfield_counter.git", "reference": "2.4.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/textfield_counter-2.4.0.zip", "reference": "2.4.0", "shasum": "0abca4ec71b4b46499c823acf60d37a09d0e2f91"}, "require": {"drupal/core": "^9 || ^10 || ^11", "php": ">=8.1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.4.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (jaypan)", "homepage": "https://www.drupal.org/u/jaypan"}, {"name": "jaypan", "homepage": "https://www.drupal.org/user/324696"}], "description": "Creates field formatters with character counts for text fields and text areas.", "homepage": "https://www.drupal.org/project/textfield_counter", "support": {"source": "https://git.drupalcode.org/project/textfield_counter", "issues": "https://www.drupal.org/project/issues/textfield_counter"}}, {"name": "drupal/token", "version": "1.15.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/token.git", "reference": "8.x-1.15"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/token-8.x-1.15.zip", "reference": "8.x-1.15", "shasum": "5916fbccc86458a5f51e71f832ac70ff4c84ebdf"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "require-dev": {"drupal/book": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.15", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Provides a user interface for the Token API, some missing core tokens.", "homepage": "https://www.drupal.org/project/token", "support": {"source": "https://git.drupalcode.org/project/token"}}, {"name": "drupal/twig_tweak", "version": "3.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/twig_tweak.git", "reference": "3.4.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/twig_tweak-3.4.0.zip", "reference": "3.4.0", "shasum": "1f47f71b4cfbad97fff11db1adc72c311bb1645e"}, "require": {"drupal/core": "^10.3 || ^11.0", "ext-json": "*", "php": ">=8.1", "twig/twig": "^3.10.3"}, "suggest": {"symfony/var-dumper": "Better dump() function for debugging Twig variables"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.4.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chi", "homepage": "https://www.drupal.org/user/556138"}], "description": "A Twig extension with some useful functions and filters for Drup<PERSON> development.", "homepage": "https://www.drupal.org/project/twig_tweak", "keywords": ["<PERSON><PERSON><PERSON>", "Twig"], "support": {"source": "https://git.drupalcode.org/project/twig_tweak", "issues": "https://www.drupal.org/project/issues/twig_tweak"}}, {"name": "drupal/unpublished_node_permissions", "version": "1.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/unpublished_node_permissions.git", "reference": "8.x-1.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/unpublished_node_permissions-8.x-1.6.zip", "reference": "8.x-1.6", "shasum": "3201e897f4661a30e0be713f3fd16980acf614cc"}, "require": {"drupal/core": "^8.9 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (Islanderweedy)", "homepage": "https://www.drupal.org/u/islanderweedy", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (Frans)", "homepage": "https://www.drupal.org/u/frans", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (Fabsgugu)", "homepage": "https://www.drupal.org/u/fabsgugu", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (jeroen.b)", "homepage": "https://www.drupal.org/u/jeroenb", "role": "Maintainer"}, {"name": "<PERSON> (matglas86)", "homepage": "https://www.drupal.org/u/matglas86", "role": "Maintainer"}, {"name": "<PERSON> (AstonVictor)", "homepage": "https://www.drupal.org/u/astonvictor", "role": "Maintainer"}], "description": "Creates permisisons per node content type to control access to unpublished nodes per content type.", "homepage": "https://www.drupal.org/project/unpublished_node_permissions", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/unpublished_node_permissions", "issues": "https://www.drupal.org/project/issues/unpublished_node_permissions"}}, {"name": "drupal/views_bulk_operations", "version": "4.3.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/views_bulk_operations.git", "reference": "4.3.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/views_bulk_operations-4.3.4.zip", "reference": "4.3.4", "shasum": "c0974356f26d49ad9e99450e9db9650de94c6010"}, "require": {"drupal/core": "^10.3 || ^11"}, "conflict": {"drush/drush": "<12.5.1"}, "require-dev": {"drush/drush": "^12 || ^13"}, "suggest": {"drush/drush": "^12 || ^13"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.3.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/graber"}, {"name": "graber", "homepage": "https://www.drupal.org/user/1599440"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}], "description": "Adds an ability to perform bulk operations on selected entities from view results. Provides an API to create such operations.", "homepage": "https://www.drupal.org/project/views_bulk_operations", "support": {"source": "https://git.drupalcode.org/project/views_bulk_operations", "issues": "https://www.drupal.org/project/issues/views_bulk_operations?version=any_4.", "docs": "https://www.drupal.org/docs/contributed-modules/views-bulk-operations-vbo"}}, {"name": "drupal/views_infinite_scroll", "version": "2.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/views_infinite_scroll.git", "reference": "2.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/views_infinite_scroll-2.0.3.zip", "reference": "2.0.3", "shasum": "3c56969f71256300226118a0f35bad66ab41306c"}, "require": {"drupal/core": "^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "Honza Pobořil", "homepage": "https://www.drupal.org/user/123612"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/143827"}, {"name": "sam152", "homepage": "https://www.drupal.org/user/1485048"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "A pager which allows an infinite scroll effect for views.", "homepage": "https://www.drupal.org/project/views_infinite_scroll", "support": {"source": "https://git.drupalcode.org/project/views_infinite_scroll"}}, {"name": "drupal/viewsreference", "version": "2.0.0-beta10", "source": {"type": "git", "url": "https://git.drupalcode.org/project/viewsreference.git", "reference": "8.x-2.0-beta10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/viewsreference-8.x-2.0-beta10.zip", "reference": "8.x-2.0-beta10", "shasum": "699c3f790d3dbe6ebcd890916409c66562a04eb4"}, "require": {"drupal/core": "^10 || ^11"}, "conflict": {"drupal/viewsreferennce": "*"}, "require-dev": {"drupal/views_ajax_history": "1.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.0-beta10", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "joekers", "homepage": "https://www.drupal.org/user/2229066"}, {"name": "NewZeal", "homepage": "https://www.drupal.org/user/93571"}, {"name": "scott_euser", "homepage": "https://www.drupal.org/user/3267594"}, {"name": "seanb", "homepage": "https://www.drupal.org/user/545912"}], "description": "Views reference", "homepage": "http://drupal.org/project/viewsreference", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/viewsreference", "issues": "https://www.drupal.org/project/issues/viewsreference"}}, {"name": "drupal/webform", "version": "6.3.0-beta2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/webform.git", "reference": "6.3.0-beta2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/webform-6.3.0-beta2.zip", "reference": "6.3.0-beta2", "shasum": "02e45bf3716c6e81846a9b77837782a43053e6f9"}, "require": {"drupal/core": "^10.3 || ^11.0"}, "require-dev": {"drupal/address": "^2.0", "drupal/captcha": "^2.0", "drupal/chosen": "^4.0", "drupal/clientside_validation": "^4.1", "drupal/clientside_validation_jquery": "*", "drupal/devel": "^5.3", "drupal/entity": "^1.5", "drupal/entity_print": "^2.15", "drupal/hal": "^2.0", "drupal/jquery_ui": "^1.7", "drupal/jquery_ui_button": "^2.1", "drupal/jquery_ui_checkboxradio": "^2.1", "drupal/jquery_ui_datepicker": "^2.1", "drupal/mailsystem": "^4.5", "drupal/metatag": "^2.0", "drupal/paragraphs": "^1.18", "drupal/select2": "1.x-dev", "drupal/smtp": "^1.4", "drupal/styleguide": "^2.1", "drupal/telephone_validation": "2.x-dev", "drupal/token": "^1.15", "drupal/webform_access": "*", "drupal/webform_attachment": "*", "drupal/webform_clientside_validation": "*", "drupal/webform_devel": "*", "drupal/webform_entity_print": "*", "drupal/webform_node": "*", "drupal/webform_options_limit": "*", "drupal/webform_scheduled_email": "*", "drupal/webform_share": "*", "drupal/webform_ui": "*"}, "suggest": {"drupal/jquery_ui_buttons": "Provides jQuery UI Checkboxradio library. Required by the Webform jQueryUI Buttons module. The Webform jQueryUI Buttons module is deprecated because jQueryUI is no longer maintained.", "drupal/jquery_ui_datepicker": "Provides jQuery UI Datepicker library. Required to support datepickers. The Webform jQueryUI Datepicker module is deprecated because jQueryUI is no longer maintained."}, "type": "drupal-module", "extra": {"drupal": {"version": "6.3.0-beta2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}, "drush": {"services": {"drush.services.yml": ">=11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (jrockowitz)", "homepage": "https://www.drupal.org/u/jrockowitz", "role": "Maintainer"}, {"name": "Contributors", "homepage": "https://www.drupal.org/node/7404/committers", "role": "Contributor"}, {"name": "liam morland", "homepage": "https://www.drupal.org/user/493050"}, {"name": "mandclu", "homepage": "https://www.drupal.org/user/52136"}, {"name": "quicksketch", "homepage": "https://www.drupal.org/user/35821"}], "description": "Enables the creation of webforms and questionnaires.", "homepage": "https://drupal.org/project/webform", "support": {"source": "https://git.drupalcode.org/project/webform", "issues": "https://www.drupal.org/project/issues/webform?version=8.x", "docs": "https://www.drupal.org/docs/8/modules/webform", "forum": "https://drupal.stackexchange.com/questions/tagged/webform"}}, {"name": "drupal/wpf", "version": "1.2.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/wpf.git", "reference": "1.2.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/wpf-1.2.4.zip", "reference": "1.2.4", "shasum": "faa7f46b2a91416cf789ba938a001c3826962a83"}, "require": {"drupal/core": "^10.3 || ^11", "ext-gd": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.2.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/387119", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "pedrop", "homepage": "https://www.drupal.org/user/1043368"}, {"name": "szato", "homepage": "https://www.drupal.org/user/389677"}], "description": "Generates jpg copies of webp image style derivatives.", "homepage": "https://www.drupal.org/project/wpf", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/wpf", "issues": "https://www.drupal.org/project/issues/wpf"}}, {"name": "drupal/xmlsitemap", "version": "2.0.0-beta1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/xmlsitemap.git", "reference": "2.0.0-beta1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/xmlsitemap-2.0.0-beta1.zip", "reference": "2.0.0-beta1", "shasum": "83fe21b87952dce131b0fbebff24313ca0b75cdf"}, "require": {"drupal/core": "^10.3 || ^11", "ext-xmlwriter": "*"}, "require-dev": {"drupal/config_readonly": "^1.0", "drupal/metatag": "^2.0", "drupal/robotstxt": "^1.0"}, "suggest": {"drush/drush": "Command-line regenerating and rebuilding sitemaps."}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0-beta1", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}, "drush": {"services": {"drush.services.yml": ">=11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/35997"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/729614"}, {"name": "andrei.dincu", "homepage": "https://www.drupal.org/user/2628879"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "jua<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/682736"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/279003"}, {"name": "pifagor", "homepage": "https://www.drupal.org/user/2375692"}, {"name": "poker10", "homepage": "https://www.drupal.org/user/272316"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}, {"name": "ta<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3094465"}, {"name": "th_tushar", "homepage": "https://www.drupal.org/user/1835276"}], "description": "Creates XML Sitemaps for the site", "homepage": "https://www.drupal.org/project/xmlsitemap", "support": {"source": "https://git.drupalcode.org/project/xmlsitemap", "issues": "http://drupal.org/project/issues/xmlsitemap"}}, {"name": "drupal/yaml_editor", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/yaml_editor.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/yaml_editor-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "9d88d878e007db4ed5c410ad0455bbc2afdea407"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "yannickoo", "homepage": "https://www.drupal.org/user/531118"}], "description": "Adds an editor for YAML configuration textareas.", "homepage": "https://www.drupal.org/project/yaml_editor", "support": {"source": "https://git.drupalcode.org/project/yaml_editor"}}, {"name": "drush/drush", "version": "12.5.3", "source": {"type": "git", "url": "https://github.com/drush-ops/drush.git", "reference": "7fe0a492d5126c457c5fb184c4668a132b0aaac6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drush-ops/drush/zipball/7fe0a492d5126c457c5fb184c4668a132b0aaac6", "reference": "7fe0a492d5126c457c5fb184c4668a132b0aaac6", "shasum": ""}, "require": {"chi-teck/drupal-code-generator": "^3.0", "composer-runtime-api": "^2.2", "composer/semver": "^1.4 || ^3", "consolidation/annotated-command": "^4.9.2", "consolidation/config": "^2.1.2", "consolidation/filter-via-dot-access-data": "^2.0.2", "consolidation/output-formatters": "^4.3.2", "consolidation/robo": "^4.0.6", "consolidation/site-alias": "^4", "consolidation/site-process": "^5.2.0", "ext-dom": "*", "grasmash/yaml-cli": "^3.1", "guzzlehttp/guzzle": "^7.0", "league/container": "^4", "php": ">=8.1", "psy/psysh": "~0.11", "symfony/event-dispatcher": "^6", "symfony/filesystem": "^6.1", "symfony/finder": "^6", "symfony/var-dumper": "^6.0", "symfony/yaml": "^6.0", "webflo/drupal-finder": "^1.2"}, "conflict": {"drupal/core": "< 10.0", "drupal/migrate_run": "*", "drupal/migrate_tools": "<= 5"}, "require-dev": {"composer/installers": "^2", "cweagans/composer-patches": "~1.0", "drupal/core-recommended": "^10", "drupal/semver_example": "2.3.0", "phpunit/phpunit": "^9", "rector/rector": "^0.12", "squizlabs/php_codesniffer": "^3.7"}, "bin": ["drush"], "type": "library", "extra": {"installer-paths": {"sut/core": ["type:drupal-core"], "sut/libraries/{$name}": ["type:drupal-library"], "sut/themes/unish/{$name}": ["drupal/empty_theme"], "sut/drush/contrib/{$name}": ["type:drupal-drush"], "sut/modules/unish/{$name}": ["drupal/devel"], "sut/themes/contrib/{$name}": ["type:drupal-theme"], "sut/modules/contrib/{$name}": ["type:drupal-module"], "sut/profiles/contrib/{$name}": ["type:drupal-profile"]}}, "autoload": {"psr-4": {"Drush\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "j<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drush is a command line shell and scripting interface for <PERSON><PERSON><PERSON>, a veritable Swiss Army knife designed to make life easier for those of us who spend some of our working hours hacking away at the command prompt.", "homepage": "http://www.drush.org", "support": {"forum": "http://drupal.stackexchange.com/questions/tagged/drush", "issues": "https://github.com/drush-ops/drush/issues", "security": "https://github.com/drush-ops/drush/security/advisories", "slack": "https://drupal.slack.com/messages/C62H9CWQM", "source": "https://github.com/drush-ops/drush/tree/12.5.3"}, "funding": [{"url": "https://github.com/weitzman", "type": "github"}], "time": "2024-08-02T11:57:29+00:00"}, {"name": "e0ipso/shaper", "version": "1.2.7", "source": {"type": "git", "url": "https://github.com/e0ipso/shaper.git", "reference": "1d9e726c98d3168d30c1180cd836d13267744fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/e0ipso/shaper/zipball/1d9e726c98d3168d30c1180cd836d13267744fb7", "reference": "1d9e726c98d3168d30c1180cd836d13267744fb7", "shasum": ""}, "require": {"justinrainbow/json-schema": "^5.2 || ^6.3"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4", "phpunit/phpcov": "^8.2", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Shaper\\": "src", "Shaper\\Tests\\": "tests/src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Lightweight library to handle in and out transformations in PHP.", "support": {"issues": "https://github.com/e0ipso/shaper/issues", "source": "https://github.com/e0ipso/shaper/tree/1.2.7"}, "time": "2025-04-04T08:14:01+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.15.4", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "e50b83a2f1f296ca61394fe88fbfe3e896a84cf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/e50b83a2f1f296ca61394fe88fbfe3e896a84cf4", "reference": "e50b83a2f1f296ca61394fe88fbfe3e896a84cf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.15.4"}, "time": "2022-02-21T09:13:59+00:00"}, {"name": "google/recaptcha", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "d59a801e98a4e9174814a6d71bbc268dff1202df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/d59a801e98a4e9174814a6d71bbc268dff1202df", "reference": "d59a801e98a4e9174814a6d71bbc268dff1202df", "shasum": ""}, "require": {"php": ">=8"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "support": {"forum": "https://groups.google.com/forum/#!forum/recaptcha", "issues": "https://github.com/google/recaptcha/issues", "source": "https://github.com/google/recaptcha"}, "time": "2023-02-18T17:41:46+00:00"}, {"name": "grasmash/expander", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/grasmash/expander.git", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/expander/zipball/eea11b9afb0c32483b18b9009f4ca07b770e39f4", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.0", "php": ">=8.0", "psr/log": "^2 | ^3"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\Expander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in PHP arrays file.", "support": {"issues": "https://github.com/grasmash/expander/issues", "source": "https://github.com/grasmash/expander/tree/3.0.1"}, "time": "2024-11-25T23:28:05+00:00"}, {"name": "grasmash/yaml-cli", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/grasmash/yaml-cli.git", "reference": "09a8860566958a1576cc54bbe910a03477e54971"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/yaml-cli/zipball/09a8860566958a1576cc54bbe910a03477e54971", "reference": "09a8860566958a1576cc54bbe910a03477e54971", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3", "php": ">=8.0", "symfony/console": "^6 || ^7", "symfony/filesystem": "^6 || ^7", "symfony/yaml": "^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.0"}, "bin": ["bin/yaml-cli"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\YamlCli\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A command line tool for reading and manipulating yaml files.", "support": {"issues": "https://github.com/grasmash/yaml-cli/issues", "source": "https://github.com/grasmash/yaml-cli/tree/3.2.1"}, "time": "2024-04-23T02:10:57+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.1", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/35d262c94959571e8736db1e5c9bc36ab94ae900", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.1"}, "time": "2025-04-04T13:08:07+00:00"}, {"name": "league/container", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "7ea728b013b9a156c409c6f0fc3624071b742dec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/7ea728b013b9a156c409c6f0fc3624071b742dec", "reference": "7ea728b013b9a156c409c6f0fc3624071b742dec", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/container": "^1.1 || ^2.0"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"nette/php-generator": "^3.4", "nikic/php-parser": "^4.10", "phpstan/phpstan": "^0.12.47", "phpunit/phpunit": "^8.5.17", "roave/security-advisories": "dev-latest", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "support": {"issues": "https://github.com/thephpleague/container/issues", "source": "https://github.com/thephpleague/container/tree/4.2.4"}, "funding": [{"url": "https://github.com/philipobenito", "type": "github"}], "time": "2024-11-10T12:42:13+00:00"}, {"name": "league/csv", "version": "9.23.0", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "774008ad8a634448e4f8e288905e070e8b317ff3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/774008ad8a634448e4f8e288905e070e8b317ff3", "reference": "774008ad8a634448e4f8e288905e070e8b317ff3", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1.2"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.69.0", "phpbench/phpbench": "^1.4.0", "phpstan/phpstan": "^1.12.18", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.2", "phpstan/phpstan-strict-rules": "^1.6.2", "phpunit/phpunit": "^10.5.16 || ^11.5.7", "symfony/var-dumper": "^6.4.8 || ^7.2.3"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters", "ext-mysqli": "Requiered to use the package with the MySQLi extension", "ext-pdo": "Required to use the package with the PDO extension", "ext-pgsql": "Requiered to use the package with the PgSQL extension", "ext-sqlite3": "Required to use the package with the SQLite3 extension"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2025-03-28T06:52:04+00:00"}, {"name": "league/oauth2-client", "version": "2.8.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "9df2924ca644736c835fc60466a3a60390d334f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/9df2924ca644736c835fc60466a3a60390d334f9", "reference": "9df2924ca644736c835fc60466a3a60390d334f9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "php": "^7.1 || >=8.0.0 <8.5.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.11"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.8.1"}, "time": "2025-02-26T04:37:30+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mck89/peast", "version": "v1.16.3", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "645ec21b650bc2aced18285c85f220d22afc1430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/645ec21b650bc2aced18285c85f220d22afc1430", "reference": "645ec21b650bc2aced18285c85f220d22afc1430", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16.3-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.16.3"}, "time": "2024-07-23T14:00:32+00:00"}, {"name": "mglaman/composer-drupal-lenient", "version": "1.0.7", "source": {"type": "git", "url": "https://github.com/mglaman/composer-drupal-lenient.git", "reference": "bcb9be7f2d3160be43cd1d13a44580734a5afee0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mglaman/composer-drupal-lenient/zipball/bcb9be7f2d3160be43cd1d13a44580734a5afee0", "reference": "bcb9be7f2d3160be43cd1d13a44580734a5afee0", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": ">=8.1"}, "require-dev": {"composer/composer": "^2.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.6", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.2", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "composer-plugin", "extra": {"class": "ComposerDrupalLenient\\Plugin"}, "autoload": {"psr-4": {"ComposerDrupalLenient\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/mglaman/composer-drupal-lenient/issues", "source": "https://github.com/mglaman/composer-drupal-lenient/tree/1.0.7"}, "funding": [{"url": "https://github.com/mglaman", "type": "github"}], "time": "2024-11-21T15:59:26+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "time": "2024-12-30T11:07:19+00:00"}, {"name": "pear/archive_tar", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/b439c859564f5cbb0f64ad6002d0afe84a889602", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Archive_Tar", "source": "https://github.com/pear/Archive_Tar"}, "time": "2024-03-16T16:21:40+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Getopt", "source": "https://github.com/pear/Console_<PERSON>opt"}, "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.16", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/c0f51b45f50683bf5bbf558036854ebc9b54d033", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0", "php": ">=5.4"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR", "source": "https://github.com/pear/pear-core-minimal"}, "time": "2024-11-24T22:27:58+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "phootwork/collection", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/collection.git", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/collection/zipball/46dde20420fba17766c89200bc3ff91d3e58eafa", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa", "shasum": ""}, "require": {"phootwork/lang": "^3.0", "php": ">=8.0"}, "type": "library", "autoload": {"psr-4": {"phootwork\\collection\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "The phootwork library fills gaps in the php language and provides better solutions than the existing ones php offers.", "homepage": "https://phootwork.github.io/collection/", "keywords": ["Array object", "Text object", "collection", "collections", "json", "list", "map", "queue", "set", "stack", "xml"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/collection/tree/v3.2.3"}, "time": "2022-08-27T12:51:24+00:00"}, {"name": "phootwork/lang", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/lang.git", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/lang/zipball/52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "shasum": ""}, "require": {"php": ">=8.0", "symfony/polyfill-mbstring": "^1.12", "symfony/polyfill-php81": "^1.22"}, "type": "library", "autoload": {"psr-4": {"phootwork\\lang\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "Missing PHP language constructs", "homepage": "https://phootwork.github.io/lang/", "keywords": ["array", "comparator", "comparison", "string"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/lang/tree/v3.2.3"}, "time": "2024-10-03T13:43:19+00:00"}, {"name": "phpowermove/docblock", "version": "v4.0", "source": {"type": "git", "url": "https://github.com/phpowermove/docblock.git", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpowermove/docblock/zipball/a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "shasum": ""}, "require": {"phootwork/collection": "^3.0", "phootwork/lang": "^3.0", "php": ">=8.0"}, "require-dev": {"phootwork/php-cs-fixer-config": "^0.4", "phpunit/phpunit": "^9.0", "psalm/phar": "^4.3"}, "type": "library", "autoload": {"psr-4": {"phpowermove\\docblock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "PHP Docblock parser and generator. An API to read and write Docblocks.", "keywords": ["doc<PERSON>", "generator", "parser"], "support": {"issues": "https://github.com/phpowermove/docblock/issues", "source": "https://github.com/phpowermove/docblock/tree/v4.0"}, "time": "2021-09-22T16:57:06+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psy/psysh", "version": "v0.12.8", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/85057ceedee50c49d4f6ecaff73ee96adb3b3625", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "symfony/console", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "2e4af9c952617cc3f9559ff706aee420a8464c36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/2e4af9c952617cc3f9559ff706aee420a8464c36", "reference": "2e4af9c952617cc3f9559ff706aee420a8464c36", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T17:16:38+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "c49796a9184a532843e78e50df9e55708b92543a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/c49796a9184a532843e78e50df9e55708b92543a", "reference": "c49796a9184a532843e78e50df9e55708b92543a", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T09:55:08+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "aa3bcf4f7674719df078e61cc8062e5b7f752031"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/aa3bcf4f7674719df078e61cc8062e5b7f752031", "reference": "aa3bcf4f7674719df078e61cc8062e5b7f752031", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-01T13:00:38+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/d0492d6217e5ab48f51fca76f64cf8e78919d0db", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-09T15:48:56+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "6be6db31bc74693ce5516e1fd5e5ff1171005e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/6be6db31bc74693ce5516e1fd5e5ff1171005e37", "reference": "6be6db31bc74693ce5516e1fd5e5ff1171005e37", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-28T13:27:10+00:00"}, {"name": "symfony/mailer", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "e93a6ae2767d7f7578c2b7961d9d8e27580b2b11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/e93a6ae2767d7f7578c2b7961d9d8e27580b2b11", "reference": "e93a6ae2767d7f7578c2b7961d9d8e27580b2b11", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-24T15:27:15+00:00"}, {"name": "symfony/mime", "version": "v6.4.19", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3", "reference": "ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-17T21:23:52+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/e2a61c16af36c9a07e5c9906498b73e091949a20", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c9cf83326a1074f83a738fc5320945abf7fb7fec", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec", "shasum": ""}, "require": {"php": ">=8.1", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^5.4|^6.0|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.2"}, "require-dev": {"nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^6.2|^7.0", "symfony/http-kernel": "^6.2|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "https://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/routing", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "e9bfc94953019089acdfb9be51c1b9142c4afa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/e9bfc94953019089acdfb9be51c1b9142c4afa68", "reference": "e9bfc94953019089acdfb9be51c1b9142c4afa68", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-09T08:51:02+00:00"}, {"name": "symfony/serializer", "version": "v6.4.19", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "a221b2f6066af304d760cff7a26f201b4fab4aef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/a221b2f6066af304d760cff7a26f201b4fab4aef", "reference": "a221b2f6066af304d760cff7a26f201b4fab4aef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<5.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.4", "symfony/validator": "<6.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.26|^6.3|^7.0", "symfony/property-info": "^5.4.24|^6.2.11|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v6.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T08:42:36+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v6.4.15", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f", "reference": "73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:31:12+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/validator", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "9314555aceb8d8ce8abda81e1e47e439258d9309"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/9314555aceb8d8ce8abda81e1e47e439258d9309", "reference": "9314555aceb8d8ce8abda81e1e47e439258d9309", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-14T14:22:58+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "4ad10cf8b020e77ba665305bb7804389884b4837"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/4ad10cf8b020e77ba665305bb7804389884b4837", "reference": "4ad10cf8b020e77ba665305bb7804389884b4837", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T11:26:11+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "998df255e9e6a15a36ae35e9c6cd818c17cf92a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/998df255e9e6a15a36ae35e9c6cd818c17cf92a2", "reference": "998df255e9e6a15a36ae35e9c6cd818c17cf92a2", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T09:55:08+00:00"}, {"name": "symfony/yaml", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "28ee818fce4a73ac1474346b94e4b966f665c53f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/28ee818fce4a73ac1474346b94e4b966f665c53f", "reference": "28ee818fce4a73ac1474346b94e4b966f665c53f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-27T20:15:30+00:00"}, {"name": "twig/twig", "version": "v3.19.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "d4f8c2b86374f08efc859323dbcd95c590f7124e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/d4f8c2b86374f08efc859323dbcd95c590f7124e", "reference": "d4f8c2b86374f08efc859323dbcd95c590f7124e", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php81": "^1.29"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.19.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-01-29T07:06:14+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.9", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "shasum": ""}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.9"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T22:59:22+00:00"}, {"name": "webflo/drupal-finder", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/webflo/drupal-finder.git", "reference": "73045060b0894c77962a10cff047f72872d8810c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webflo/drupal-finder/zipball/73045060b0894c77962a10cff047f72872d8810c", "reference": "73045060b0894c77962a10cff047f72872d8810c", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "php": ">=8.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^10.4", "symfony/process": "^6.4"}, "type": "library", "autoload": {"psr-4": {"DrupalFinder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Helper class to locate a <PERSON><PERSON><PERSON> installation.", "support": {"issues": "https://github.com/webflo/drupal-finder/issues", "source": "https://github.com/webflo/drupal-finder/tree/1.3.1"}, "time": "2024-06-28T13:45:36+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.3.0"}, "abandoned": "symfony/filesystem", "time": "2015-12-17T08:42:14+00:00"}, {"name": "wimc/wimc_admin", "version": "v3.0.51", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_admin/v3.0.51/wimc-wimc_admin-v3.0.51.zip", "reference": "82d2fbb56a488d62f8a8ec4e4c302f75e891cd3f", "shasum": "82d2fbb56a488d62f8a8ec4e4c302f75e891cd3f"}, "type": "drupal-module", "description": "Customized administration UX", "time": "2025-05-21T18:35:13+00:00"}, {"name": "wimc/wimc_analytics", "version": "v1.0.10", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_analytics/v1.0.10/wimc-wimc_analytics-v1.0.10.zip", "reference": "37aedcd18b74e51d674adf351b149782169f8115", "shasum": "37aedcd18b74e51d674adf351b149782169f8115"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Default page analytics module", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_analytics", "keywords": ["analytics", "load_page"], "time": "2025-03-17T15:02:37+00:00"}, {"name": "wimc/wimc_api", "version": "v3.0.55", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_api/v3.0.55/wimc-wimc_api-v3.0.55.zip", "reference": "c14e4fa87c7e9bb08fa940d84e33e0d5fab70843", "shasum": "c14e4fa87c7e9bb08fa940d84e33e0d5fab70843"}, "type": "drupal-module", "description": "Expose contents through an API", "time": "2025-05-22T08:19:39+00:00"}, {"name": "wimc/wimc_ckeditor5", "version": "v2.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_ckeditor5/v2.0.0/wimc-wimc_ckeditor5-v2.0.0.zip", "reference": "5bf0c48cc2b66ea081b6534fcc3e0d37b7dd6374", "shasum": "5bf0c48cc2b66ea081b6534fcc3e0d37b7dd6374"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "CKEditor 5 module that adds some custom ckeditor5 plugins to ckeditor. For example, tooltips can be added directly in the text editor.", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_ckeditor5", "keywords": ["backend-support", "ckeditor"], "time": "2024-07-08T10:47:44+00:00"}, {"name": "wimc/wimc_contact", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_contact/v3.0.0/wimc-wimc_contact-v3.0.0.zip", "reference": "35e2fed7212abb8e78af1baf3a7b5af42e7f792b", "shasum": "35e2fed7212abb8e78af1baf3a7b5af42e7f792b"}, "type": "drupal-module", "description": "Expose contact form through an API.", "time": "2023-02-09T06:42:14+00:00"}, {"name": "wimc/wimc_customer_landingpage", "version": "v2.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_customer_landingpage/v2.0.0/wimc-wimc_customer_landingpage-v2.0.0.zip", "reference": "509bf1c6e6ed798f8b3b38e21180257bba485092", "shasum": "509bf1c6e6ed798f8b3b38e21180257bba485092"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Provide functionalities for Customized Landing Page", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_customer_landingpage", "keywords": ["marketing", "ome"], "time": "2025-04-02T10:06:40+00:00"}, {"name": "wimc/wimc_entity_share", "version": "v2.0.1", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_entity_share/v2.0.1/wimc-wimc_entity_share-v2.0.1.zip", "reference": "9a220f104783e9b096991bae66d69e7db282991c", "shasum": "9a220f104783e9b096991bae66d69e7db282991c"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Extends the Entity Share module functionalities", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_entity_share", "keywords": ["entity-share", "share", "sync"], "time": "2024-03-04T06:54:48+00:00"}, {"name": "wimc/wimc_faqs", "version": "v3.0.5", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_faqs/v3.0.5/wimc-wimc_faqs-v3.0.5.zip", "reference": "be80a3986b7e1fd63d0809ec9a7f30c8bfb58df0", "shasum": "be80a3986b7e1fd63d0809ec9a7f30c8bfb58df0"}, "type": "drupal-module", "description": "Extend the Faqs functionalities", "time": "2025-01-22T08:34:55+00:00"}, {"name": "wimc/wimc_full_content_share", "version": "v2.0.1", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_full_content_share/v2.0.1/wimc-wimc_full_content_share-v2.0.1.zip", "reference": "a78d9e463f4a0b63f6bccc73f1c6f9931b35c35c", "shasum": "a78d9e463f4a0b63f6bccc73f1c6f9931b35c35c"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Provide admin interface to push full site content (entities and asset) to a target environment.", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_full_content_share", "keywords": ["backend", "backend_experience", "content-upload-improvement"], "time": "2025-03-19T14:49:24+00:00"}, {"name": "wimc/**********************", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/**********************/v3.0.1/wimc-**********************-v3.0.1.zip", "reference": "8ed31e2a4fe17e207fd724edc0bbbf1052278107", "shasum": "8ed31e2a4fe17e207fd724edc0bbbf1052278107"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Provide admin interface for GitLab Integration.", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/**********************", "keywords": ["backend", "backend_experience", "content-upload-improvement"], "time": "2025-03-19T14:49:56+00:00"}, {"name": "wimc/wimc_gtm_consent", "version": "v1.0.13", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_gtm_consent/v1.0.13/wimc-wimc_gtm_consent-v1.0.13.zip", "reference": "f7c0445cc6fa91583431364550461dce4ae5b39b", "shasum": "f7c0445cc6fa91583431364550461dce4ae5b39b"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Provide Google Tag Manager Consent features", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_gtm_consent", "keywords": ["consent", "gtm"], "time": "2025-05-07T06:49:56+00:00"}, {"name": "wimc/wimc_paragraphs", "version": "v3.0.65", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_paragraphs/v3.0.65/wimc-wimc_paragraphs-v3.0.65.zip", "reference": "167084964432d40393a720c4838102576e66160d", "shasum": "167084964432d40393a720c4838102576e66160d"}, "require": {"wimc/wimc_tuffy_connector": "^2.0.0"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "description": "Extend the paragraph functionalities", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_paragraphs", "keywords": ["entity", "paragraph", "trait"], "time": "2025-04-08T12:49:24+00:00"}, {"name": "wimc/wimc_posthog", "version": "v1.0.2", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_posthog/v1.0.2/wimc-wimc_posthog-v1.0.2.zip", "reference": "f6ef6dd9e10bdc4d10499ad9ac5dbf3ac651594e", "shasum": "f6ef6dd9e10bdc4d10499ad9ac5dbf3ac651594e"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "description": "JavaScript Web integration of PostHog", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_posthog", "keywords": ["entity", "paragraph", "trait"], "time": "2025-03-19T14:53:22+00:00"}, {"name": "wimc/wimc_publication_scheduler", "version": "v1.0.4", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_publication_scheduler/v1.0.4/wimc-wimc_publication_scheduler-v1.0.4.zip", "reference": "a2fe7a8b9b6ee149ae26894037b464c47a5f642f", "shasum": "a2fe7a8b9b6ee149ae26894037b464c47a5f642f"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Provide additional functionalities to the scheduler contrib module. Keep the dates in custom fields.", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_publication_scheduler", "keywords": ["scheduler"], "time": "2025-05-06T09:45:37+00:00"}, {"name": "wimc/wimc_radio_channels", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_radio_channels/v3.0.0/wimc-wimc_radio_channels-v3.0.0.zip", "reference": "8a21ccd2bc536885422c7d9a82960f7eeb232ade", "shasum": "8a21ccd2bc536885422c7d9a82960f7eeb232ade"}, "type": "drupal-module", "description": "Radio channels import", "time": "2023-02-28T06:29:12+00:00"}, {"name": "wimc/wimc_roaming", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_roaming/v3.0.0/wimc-wimc_roaming-v3.0.0.zip", "reference": "c2bb9f2cbe65e75b81450e5b0bca4fe42709845a", "shasum": "c2bb9f2cbe65e75b81450e5b0bca4fe42709845a"}, "type": "drupal-module", "description": "Roaming functionalities", "time": "2023-02-09T06:29:47+00:00"}, {"name": "wimc/wimc_seo", "version": "v3.0.5", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_seo/v3.0.5/wimc-wimc_seo-v3.0.5.zip", "reference": "e098c9a11a191b9345e700230bd20bf8b29caf30", "shasum": "e098c9a11a191b9345e700230bd20bf8b29caf30"}, "type": "drupal-module", "description": "Search engine optimizations", "time": "2025-02-05T08:08:22+00:00"}, {"name": "wimc/**********************", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/**********************/v3.0.0/wimc-**********************-v3.0.0.zip", "reference": "ef2459547ca131f3c6d8ba3f5aac0682c3921802", "shasum": "ef2459547ca131f3c6d8ba3f5aac0682c3921802"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Short URL Manager project as Dr<PERSON><PERSON> module, expose short url entities", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/**********************", "keywords": ["backend-support", "short-url"], "time": "2023-08-17T10:13:29+00:00"}, {"name": "wimc/wimc_smartphones", "version": "v4.0.7", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_smartphones/v4.0.7/wimc-wimc_smartphones-v4.0.7.zip", "reference": "72e8dc868fb34d375f37cc5d3284f8053fd07745", "shasum": "72e8dc868fb34d375f37cc5d3284f8053fd07745"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Smartphones (former device deals) functionalities", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_smartphones", "keywords": ["deal", "device", "device-deal", "smartphone"], "time": "2025-03-19T14:56:39+00:00"}, {"name": "wimc/wimc_teaser_fields", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_teaser_fields/v3.0.0/wimc-wimc_teaser_fields-v3.0.0.zip", "reference": "0022ebdf6580381674b3043dc5d3a06ad6067142", "shasum": "0022ebdf6580381674b3043dc5d3a06ad6067142"}, "type": "drupal-module", "description": "Set the teaser fields for new created nodes", "time": "2023-02-09T06:25:14+00:00"}, {"name": "wimc/wimc_tuffy_connector", "version": "v2.0.5", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_tuffy_connector/v2.0.5/wimc-wimc_tuffy_connector-v2.0.5.zip", "reference": "fe67f3493c34ac022d026be0ed75226f40546705", "shasum": "fe67f3493c34ac022d026be0ed75226f40546705"}, "type": "drupal-module", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Elca Wingo Support ", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Custom Http client to tuffy with exception management and services factory", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/wimc_tuffy_connector", "keywords": ["guzzle", "tuffy"], "time": "2025-03-19T14:54:58+00:00"}, {"name": "wimc/wimc_tv_channels", "version": "v3.0.0", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_tv_channels/v3.0.0/wimc-wimc_tv_channels-v3.0.0.zip", "reference": "20cfb2e3ae1084ecdc62e12de140d31a9616f950", "shasum": "20cfb2e3ae1084ecdc62e12de140d31a9616f950"}, "type": "drupal-module", "description": "Tv channels functionalities", "time": "2023-02-09T06:22:44+00:00"}], "packages-dev": [{"name": "drupal/j<PERSON>y_ui", "version": "1.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jquery_ui.git", "reference": "8.x-1.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jquery_ui-8.x-1.7.zip", "reference": "8.x-1.7", "shasum": "3f893843ec30fed18fa1b0cb326e51880b0cb686"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "bnjmnm", "homepage": "https://www.drupal.org/user/2369194"}, {"name": "j<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/17190"}, {"name": "lauriii", "homepage": "https://www.drupal.org/user/1078742"}, {"name": "litwol", "homepage": "https://www.drupal.org/user/78134"}, {"name": "mfb", "homepage": "https://www.drupal.org/user/12302"}, {"name": "mfer", "homepage": "https://www.drupal.org/user/25701"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2972409"}, {"name": "nod_", "homepage": "https://www.drupal.org/user/598310"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "RobLoach", "homepage": "https://www.drupal.org/user/61114"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "webchick", "homepage": "https://www.drupal.org/user/24967"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/99777"}, {"name": "zrpnr", "homepage": "https://www.drupal.org/user/1448368"}], "description": "Provides jQuery UI library.", "homepage": "https://www.drupal.org/project/jquery_ui", "support": {"source": "https://git.drupalcode.org/project/jquery_ui"}}, {"name": "friendsoftwig/twigcs", "version": "6.5.0", "source": {"type": "git", "url": "https://github.com/friendsoftwig/twigcs.git", "reference": "aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/friendsoftwig/twigcs/zipball/aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc", "reference": "aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc", "shasum": ""}, "require": {"ext-ctype": "*", "ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "symfony/console": "^4.4 || ^5.3 || ^6.0 || ^7.0", "symfony/filesystem": "^4.4 || ^5.3 || ^6.0 || ^7.0", "symfony/finder": "^4.4 || ^5.3 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^9.6.19", "symfony/phpunit-bridge": "^7.1.4"}, "bin": ["bin/twigcs"], "type": "library", "autoload": {"psr-4": {"FriendsOfTwig\\Twigcs\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Checkstyle automation for Twig", "support": {"issues": "https://github.com/friendsoftwig/twigcs/issues", "source": "https://github.com/friendsoftwig/twigcs/tree/6.5.0"}, "time": "2024-11-27T21:59:24+00:00"}, {"name": "wimc/wimc_devops", "version": "v1.0.4", "dist": {"type": "zip", "url": "https://nexus.wingo.ch/repository/composer-elca/wimc/wimc_devops/v1.0.4/wimc-wimc_devops-v1.0.4.zip", "reference": "359d287c772c95a3cbadd40c49022f653651cab6", "shasum": "359d287c772c95a3cbadd40c49022f653651cab6"}, "type": "library", "license": ["GPL-2.0-or-later"], "description": "DevOps helpers module for WIMC projects", "homepage": "https://gitlab.wingo.ch/eshop-dev-team/eshop-gitlab-ci-templates", "keywords": ["bash", "devops", "shell"], "time": "2025-05-27T07:49:24+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"drupal/advanced_text_formatter": 5, "drupal/content_translation_redirect": 15, "drupal/entity_share": 5, "drupal/entity_usage": 10, "drupal/mimemail": 15, "drupal/node_revision_delete": 5, "drupal/viewsreference": 10, "drupal/webform": 10, "drupal/xmlsitemap": 10}, "prefer-stable": true, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}