diff --git a/migrate_tools/src/MigrateExecutable.php b/migrate_tools/src/MigrateExecutable.php
index d82eb54..d245133 100755
--- a/migrate_tools/src/MigrateExecutable.php
+++ b/migrate_tools/src/MigrateExecutable.php
@@ -88,7 +88,7 @@ class MigrateExecutable extends MigrateExecutableBase {
   /**
    * {@inheritdoc}
    */
-  public function __construct(MigrationInterface $migration, MigrateMessageInterface $message = NULL, array $options = []) {
+  public function __construct(MigrationInterface $migration, ?MigrateMessageInterface $message = NULL, array $options = []) {
     parent::__construct($migration, $message);
     if (isset($options['limit'])) {
       $this->itemLimit = $options['limit'];
diff --git a/migrate_tools/src/Routing/RouteProcessor.php b/migrate_tools/src/Routing/RouteProcessor.php
index 4ad2155..a993b68 100644
--- a/migrate_tools/src/Routing/RouteProcessor.php
+++ b/migrate_tools/src/Routing/RouteProcessor.php
@@ -23,7 +23,7 @@ class RouteProcessor implements OutboundRouteProcessorInterface {
   /**
    * {@inheritdoc}
    */
-  public function processOutbound($route_name, Route $route, array &$parameters, BubbleableMetadata $bubbleable_metadata = NULL): void {
+  public function processOutbound($route_name, Route $route, array &$parameters, ?BubbleableMetadata $bubbleable_metadata = NULL): void {
     if ($route->hasDefault('_migrate_group')) {
       $parameters['migration_group'] = 'default';
       if ($this->entityTypeManager->hasHandler('migration', 'storage')) {
