uuid: d3a30e1f-bc79-4af6-8d3e-8941aac6e78d
langcode: en
status: true
dependencies:
  config:
    - field.field.node.radio_channel.field_cm_brands_hidden
    - field.field.node.radio_channel.field_cm_cms_title
    - field.field.node.radio_channel.field_radio_channel_id
    - field.field.node.radio_channel.field_radio_channel_language
    - field.field.node.radio_channel.field_radio_channel_logo
    - node.type.radio_channel
  module:
    - user
id: node.radio_channel.default
targetEntityType: node
bundle: radio_channel
mode: default
content:
  field_cm_brands_hidden:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  field_cm_cms_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 101
    region: content
  field_radio_channel_id:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  field_radio_channel_language:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 104
    region: content
  field_radio_channel_logo:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 105
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
  search_api_excerpt: true
