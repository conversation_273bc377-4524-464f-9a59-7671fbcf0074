uuid: 79bbf3ca-bbf3-4d5d-8370-bb092a38096b
langcode: en
status: true
dependencies:
  config:
    - field.field.node.smartphone_variant.field_breadcrumb_title
    - field.field.node.smartphone_variant.field_cm_analytics_detail_event
    - field.field.node.smartphone_variant.field_cm_analytics_page_type
    - field.field.node.smartphone_variant.field_cm_cms_title
    - field.field.node.smartphone_variant.field_cm_datalayer_pnam
    - field.field.node.smartphone_variant.field_smartphone_base
    - field.field.node.smartphone_variant.field_smartphone_catalog_price
    - field.field.node.smartphone_variant.field_smartphone_color_data
    - field.field.node.smartphone_variant.field_smartphone_color_id
    - field.field.node.smartphone_variant.field_smartphone_features
    - field.field.node.smartphone_variant.field_smartphone_image
    - field.field.node.smartphone_variant.field_smartphone_images
    - field.field.node.smartphone_variant.field_smartphone_is_new
    - field.field.node.smartphone_variant.field_smartphone_material_number
    - field.field.node.smartphone_variant.field_smartphone_memory_data
    - field.field.node.smartphone_variant.field_smartphone_memory_id
    - field.field.node.smartphone_variant.field_smartphone_monthly_charge
    - field.field.node.smartphone_variant.field_smartphone_normal_color
    - field.field.node.smartphone_variant.field_smartphone_num_of_install
    - field.field.node.smartphone_variant.field_smartphone_seo_name
    - field.field.node.smartphone_variant.field_smartphone_stock_level
    - field.field.node.smartphone_variant.field_smartphone_street_price
    - field.field.node.smartphone_variant.field_smartphone_upfront_fee
    - node.type.smartphone_variant
  module:
    - user
id: node.smartphone_variant.default
targetEntityType: node
bundle: smartphone_variant
mode: default
content:
  field_breadcrumb_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 124
    region: content
  field_cm_analytics_detail_event:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 106
    region: content
  field_cm_analytics_page_type:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 107
    region: content
  field_cm_cms_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 102
    region: content
  field_cm_datalayer_pnam:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 108
    region: content
  field_smartphone_base:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 105
    region: content
  field_smartphone_catalog_price:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 120
    region: content
  field_smartphone_color_data:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 112
    region: content
  field_smartphone_color_id:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 110
    region: content
  field_smartphone_features:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 113
    region: content
  field_smartphone_image:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 114
    region: content
  field_smartphone_images:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 115
    region: content
  field_smartphone_is_new:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 104
    region: content
  field_smartphone_material_number:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  field_smartphone_memory_data:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 117
    region: content
  field_smartphone_memory_id:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 116
    region: content
  field_smartphone_monthly_charge:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 122
    region: content
  field_smartphone_normal_color:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 111
    region: content
  field_smartphone_num_of_install:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 123
    region: content
  field_smartphone_seo_name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 101
    region: content
  field_smartphone_stock_level:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 118
    region: content
  field_smartphone_street_price:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 119
    region: content
  field_smartphone_upfront_fee:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 121
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
  search_api_excerpt: true
