uuid: a0f4ac4c-fc2c-4530-9c5f-abdf77c227ea
langcode: en
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.news
    - taxonomy.vocabulary.brands
id: paragraph.news.brands_hidden
field_name: brands_hidden
entity_type: paragraph
bundle: news
label: 'Hide on brands'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: default
  handler_settings:
    target_bundles:
      brands: brands
field_type: entity_reference
