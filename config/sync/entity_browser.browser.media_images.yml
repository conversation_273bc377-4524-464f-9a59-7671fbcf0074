uuid: 79dd743b-195d-40dd-9755-05dc25aa3746
langcode: en
status: true
dependencies:
  config:
    - views.view.media_browser
  module:
    - views
name: media_images
label: Images
display: modal
display_configuration:
  width: ''
  height: ''
  link_text: Select
  auto_open: false
selection_display: no_display
selection_display_configuration: {  }
widget_selector: tabs
widget_selector_configuration: {  }
widgets:
  bbb8a5a5-d55e-4ba5-bf9a-c042ad8f598d:
    id: view
    uuid: bbb8a5a5-d55e-4ba5-bf9a-c042ad8f598d
    label: Library
    weight: -10
    settings:
      submit_text: Select
      auto_select: false
      view: media_browser
      view_display: media_images
  75dd9bf3-af3a-4417-aa83-fa1d3e01c8d9:
    id: media_image_upload
    uuid: 75dd9bf3-af3a-4417-aa83-fa1d3e01c8d9
    label: Upload
    weight: 3
    settings:
      submit_text: Upload
      upload_location: 'public://'
      multiple: '1'
      extensions: 'jpg jpeg png gif'
      media_type: image
