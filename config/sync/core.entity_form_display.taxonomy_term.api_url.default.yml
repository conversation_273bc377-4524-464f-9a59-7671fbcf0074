uuid: 00f6d6f3-4124-4a0b-8498-4419e278660c
langcode: fr
status: true
dependencies:
  config:
    - field.field.taxonomy_term.api_url.field_cm_id
    - taxonomy.vocabulary.api_url
id: taxonomy_term.api_url.default
targetEntityType: taxonomy_term
bundle: api_url
mode: default
content:
  field_cm_id:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  description: true
  path: true
  publish_on: true
  publish_state: true
  translation: true
  unpublish_on: true
  unpublish_state: true
