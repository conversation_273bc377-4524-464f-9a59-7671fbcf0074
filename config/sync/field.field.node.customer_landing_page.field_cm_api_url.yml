uuid: 8074412c-fc8e-478f-9cbf-62cfbf519413
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_api_url
    - node.type.customer_landing_page
    - taxonomy.vocabulary.api_url
id: node.customer_landing_page.field_cm_api_url
field_name: field_cm_api_url
entity_type: node
bundle: customer_landing_page
label: 'Dynamic Data Source'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      api_url: api_url
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
