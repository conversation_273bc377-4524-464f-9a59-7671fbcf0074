uuid: 7fadbcc7-6962-4bed-96c4-22eaa1c20f48
langcode: de
status: true
dependencies:
  module:
    - options
    - paragraphs
id: paragraph.field_cm_space
field_name: field_cm_space
entity_type: paragraph
type: list_string
settings:
  allowed_values:
    -
      value: mb-96
      label: '96'
    -
      value: mb-80
      label: '80'
    -
      value: mb-72
      label: '72'
    -
      value: mb-64
      label: '64'
    -
      value: mb-60
      label: '60'
    -
      value: mb-56
      label: '56'
    -
      value: mb-52
      label: '52'
    -
      value: mb-48
      label: '48'
    -
      value: mb-44
      label: '44'
    -
      value: mb-40
      label: '40'
    -
      value: mb-36
      label: '36'
    -
      value: mb-32
      label: '32'
    -
      value: mb-28
      label: '28'
    -
      value: mb-24
      label: '24'
    -
      value: mb-20
      label: '20'
    -
      value: mb-16
      label: '16'
    -
      value: mb-14
      label: '14'
    -
      value: mb-12
      label: '12'
    -
      value: mb-10
      label: '10'
    -
      value: mb-9
      label: '9'
    -
      value: mb-8
      label: '8'
    -
      value: mb-7
      label: '7'
    -
      value: mb-6
      label: '6'
    -
      value: mb-5
      label: '5'
    -
      value: mb-4
      label: '4'
    -
      value: mb-3
      label: '3'
    -
      value: mb-2
      label: '2'
    -
      value: mb-1
      label: '1'
    -
      value: mb-0
      label: '0'
    -
      value: '-mb-1'
      label: '-1'
    -
      value: '-mb-2'
      label: '-2'
    -
      value: '-mb-3'
      label: '-3'
    -
      value: '-mb-4'
      label: '-4'
    -
      value: '-mb-5'
      label: '-5'
    -
      value: '-mb-6'
      label: '-6'
    -
      value: '-mb-7'
      label: '-7'
    -
      value: '-mb-8'
      label: '-8'
    -
      value: '-mb-9'
      label: '-9'
    -
      value: '-mb-10'
      label: '-10'
    -
      value: '-mb-12'
      label: '-12'
    -
      value: '-mb-14'
      label: '-14'
    -
      value: '-mb-16'
      label: '-16'
    -
      value: '-mb-20'
      label: '-20'
    -
      value: '-mb-24'
      label: '-24'
    -
      value: '-mb-28'
      label: '-28'
    -
      value: '-mb-32'
      label: '-32'
    -
      value: '-mb-36'
      label: '-36'
    -
      value: '-mb-40'
      label: '-40'
    -
      value: '-mb-44'
      label: '-44'
    -
      value: '-mb-48'
      label: '-48'
    -
      value: '-mb-52'
      label: '-52'
    -
      value: '-mb-56'
      label: '-56'
    -
      value: '-mb-60'
      label: '-60'
    -
      value: '-mb-64'
      label: '-64'
    -
      value: '-mb-72'
      label: '-72'
    -
      value: '-mb-80'
      label: '-80'
    -
      value: '-mb-96'
      label: '-96'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
