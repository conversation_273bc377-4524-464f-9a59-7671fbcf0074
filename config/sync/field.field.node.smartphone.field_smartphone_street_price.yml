uuid: 344b3830-a2ae-4597-a11d-09fd33103565
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_smartphone_street_price
    - node.type.smartphone
_core:
  default_config_hash: VFUnBL1elZ34nrThWG3lB8eKt11Ub1P5gl2_3KjBmrs
id: node.smartphone.field_smartphone_street_price
field_name: field_smartphone_street_price
entity_type: node
bundle: smartphone
label: 'Street Price'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: float
