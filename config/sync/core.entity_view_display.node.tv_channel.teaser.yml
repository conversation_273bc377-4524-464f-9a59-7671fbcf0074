uuid: ce5a9b19-5257-4cfa-bf97-4b79c285d200
langcode: en
status: false
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.tv_channel.field_cm_brands_hidden
    - field.field.node.tv_channel.field_cm_cms_title
    - field.field.node.tv_channel.field_cm_replay_ads
    - field.field.node.tv_channel.field_tv_channel_id
    - field.field.node.tv_channel.field_tv_channel_language
    - field.field.node.tv_channel.field_tv_channel_logo
    - field.field.node.tv_channel.field_tv_channel_recording
    - field.field.node.tv_channel.field_tv_channel_replay
    - field.field.node.tv_channel.field_tv_channel_resolutions
    - node.type.tv_channel
  module:
    - user
id: node.tv_channel.teaser
targetEntityType: node
bundle: tv_channel
mode: teaser
content:
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_cm_brands_hidden: true
  field_cm_cms_title: true
  field_cm_replay_ads: true
  field_tv_channel_id: true
  field_tv_channel_language: true
  field_tv_channel_logo: true
  field_tv_channel_recording: true
  field_tv_channel_replay: true
  field_tv_channel_resolutions: true
  langcode: true
  search_api_excerpt: true
