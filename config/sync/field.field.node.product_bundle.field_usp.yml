uuid: ee333ee6-a54c-414f-890c-1a65a44ea7f5
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_usp
    - node.type.product_bundle
    - taxonomy.vocabulary.promotions
id: node.product_bundle.field_usp
field_name: field_usp
entity_type: node
bundle: product_bundle
label: USP
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      promotions: promotions
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
