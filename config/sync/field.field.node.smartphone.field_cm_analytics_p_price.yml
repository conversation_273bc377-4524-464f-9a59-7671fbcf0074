uuid: d0a97114-807d-41ee-9345-be73aad090e4
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_cm_analytics_p_price
    - node.type.smartphone
_core:
  default_config_hash: UOjamepXR9_IcB-oI58JOJDntEkFuRRmxTVP6IAZBJU
id: node.smartphone.field_cm_analytics_p_price
field_name: field_cm_analytics_p_price
entity_type: node
bundle: smartphone
label: 'Product price'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: 0.0
  max: null
  prefix: ''
  suffix: ''
field_type: float
