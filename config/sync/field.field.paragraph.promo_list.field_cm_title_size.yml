uuid: aed5ee45-ad5c-4733-ba42-e4433a935513
langcode: de
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_title_size
    - paragraphs.paragraphs_type.promo_list
  module:
    - options
id: paragraph.promo_list.field_cm_title_size
field_name: field_cm_title_size
entity_type: paragraph
bundle: promo_list
label: 'Title size'
description: ''
required: false
translatable: false
default_value:
  -
    value: h1
default_value_callback: ''
settings: {  }
field_type: list_string
