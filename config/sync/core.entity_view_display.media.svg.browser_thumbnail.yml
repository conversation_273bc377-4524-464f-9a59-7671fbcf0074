uuid: e4bf0026-51bf-47f4-9c5a-5e16ba0fd778
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.browser_thumbnail
    - field.field.media.svg.field_cm_brands_hidden
    - field.field.media.svg.field_cm_media_cms_name
    - field.field.media.svg.field_context
    - field.field.media.svg.field_media_image
    - image.style.media_browser_thumbnail
    - media.type.svg
  module:
    - svg_image
id: media.svg.browser_thumbnail
targetEntityType: media
bundle: svg
mode: browser_thumbnail
content:
  name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: media_browser_thumbnail
      image_loading:
        attribute: lazy
      svg_attributes:
        width: 200
        height: 200
      svg_render_as_image: true
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_cm_brands_hidden: true
  field_cm_media_cms_name: true
  field_context: true
  field_media_image: true
  langcode: true
  search_api_excerpt: true
  uid: true
