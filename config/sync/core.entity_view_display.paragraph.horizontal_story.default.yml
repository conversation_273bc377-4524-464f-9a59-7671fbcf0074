uuid: cdfe49af-3372-4749-85d8-e3ee3e81b08c
langcode: de
status: true
dependencies:
  config:
    - field.field.paragraph.horizontal_story.field_cm_content
    - field.field.paragraph.horizontal_story.field_image_large
    - field.field.paragraph.horizontal_story.field_image_medium
    - field.field.paragraph.horizontal_story.field_image_small
    - field.field.paragraph.horizontal_story.field_title
    - paragraphs.paragraphs_type.horizontal_story
  module:
    - entity_reference_revisions
    - text
id: paragraph.horizontal_story.default
targetEntityType: paragraph
bundle: horizontal_story
mode: default
content:
  field_cm_content:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 4
    region: content
  field_image_large:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_image_medium:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_image_small:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_title:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
hidden: {  }
