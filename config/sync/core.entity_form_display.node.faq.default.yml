uuid: 67666e35-de5f-402d-bb2d-fc4cdc424e9c
langcode: en
status: true
dependencies:
  config:
    - field.field.node.faq.field_allow_content_sync
    - field.field.node.faq.field_allow_nested_sync
    - field.field.node.faq.field_breadcrumb_title
    - field.field.node.faq.field_cm_analytics_brand_name
    - field.field.node.faq.field_cm_analytics_detail_event
    - field.field.node.faq.field_cm_analytics_page_type
    - field.field.node.faq.field_cm_cms_title
    - field.field.node.faq.field_cm_content
    - field.field.node.faq.field_cm_datalayer_pnam
    - field.field.node.faq.field_cm_metatags
    - field.field.node.faq.field_cm_revisions_amount
    - field.field.node.faq.field_cm_tags
    - field.field.node.faq.field_cm_text
    - field.field.node.faq.field_content_categories
    - field.field.node.faq.field_faq_id
    - field.field.node.faq.field_faq_question
    - field.field.node.faq.field_visible_only_on_clp
    - node.type.faq
    - workflows.workflow.content_scheduler
  module:
    - content_moderation
    - cshs
    - field_group
    - metatag
    - paragraphs
    - path
    - scheduler
    - scheduler_content_moderation_integration
    - text
    - textfield_counter
third_party_settings:
  field_group:
    group_wrapper:
      children:
        - group_content
        - group_cms_categorization
        - group_analytics
        - group_seo
        - group_revisions
        - group__scheduling_options_validi
        - group_synchronization
      label: Wrapper
      region: content
      parent_name: ''
      weight: 0
      format_type: tabs
      format_settings:
        classes: ''
        id: ''
        direction: horizontal
    group_content:
      children:
        - langcode
        - field_cm_cms_title
        - title
        - field_faq_id
        - field_faq_question
        - field_cm_text
        - field_cm_content
      label: Content
      region: content
      parent_name: group_wrapper
      weight: 8
      format_type: tab
      format_settings:
        classes: ''
        id: ''
        formatter: open
        description: ''
        required_fields: true
    group_analytics:
      children:
        - field_cm_analytics_detail_event
        - field_cm_analytics_brand_name
        - field_cm_datalayer_pnam
        - field_cm_analytics_page_type
      label: Analytics
      region: content
      parent_name: group_wrapper
      weight: 10
      format_type: tab
      format_settings:
        classes: ''
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_revisions:
      children:
        - field_cm_revisions_amount
      label: Revisions
      region: content
      parent_name: group_wrapper
      weight: 12
      format_type: tab
      format_settings:
        classes: ''
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_cms_categorization:
      children:
        - field_visible_only_on_clp
        - field_cm_tags
        - field_content_categories
      label: Classification
      region: content
      parent_name: group_wrapper
      weight: 9
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group__scheduling_options_validi:
      children:
        - group_scheduler_dates
        - group_variant_validity_dates
      label: 'Scheduling Options / Validity'
      region: content
      parent_name: group_wrapper
      weight: 13
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_scheduler_dates:
      children:
        - publish_on
        - publish_state
        - unpublish_on
        - unpublish_state
      label: 'Scheduler Dates'
      region: content
      parent_name: group__scheduling_options_validi
      weight: 2
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_variant_validity_dates:
      children:
        - start_date
        - end_date
      label: 'Variant Validity Dates'
      region: content
      parent_name: group__scheduling_options_validi
      weight: 3
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_synchronization:
      children:
        - field_allow_content_sync
        - field_allow_nested_sync
      label: Synchronization
      region: content
      parent_name: group_wrapper
      weight: 14
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_seo:
      children:
        - cm_meta_title
        - cm_meta_description
        - field_breadcrumb_title
      label: SEO
      region: content
      parent_name: group_wrapper
      weight: 11
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
id: node.faq.default
targetEntityType: node
bundle: faq
mode: default
content:
  cm_meta_description:
    type: string_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  cm_meta_title:
    type: string_textarea
    weight: 0
    region: content
    settings:
      rows: 1
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: 0
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  end_date:
    type: datetime_timestamp_no_default
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  field_allow_content_sync:
    type: boolean_checkbox
    weight: 30
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_allow_nested_sync:
    type: boolean_checkbox
    weight: 31
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_breadcrumb_title:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_analytics_brand_name:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_cm_analytics_detail_event:
    type: boolean_checkbox
    weight: 0
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_cm_analytics_page_type:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_cms_title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_content:
    type: paragraphs
    weight: 9
    region: content
    settings:
      title: Seitenabschnitt
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cm_datalayer_pnam:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_metatags:
    type: metatag_firehose
    weight: 8
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  field_cm_revisions_amount:
    type: number
    weight: 1
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cm_tags:
    type: entity_reference_autocomplete_tags
    weight: 9
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 150
      placeholder: ''
    third_party_settings: {  }
  field_cm_text:
    type: text_textarea
    weight: 8
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_content_categories:
    type: cshs
    weight: 10
    region: content
    settings:
      save_lineage: false
      force_deepest: false
      parent: null
      level_labels: ''
      hierarchy_depth: 0
      required_depth: 0
      none_label: '- Please select -'
    third_party_settings: {  }
  field_faq_id:
    type: number
    weight: 6
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_faq_question:
    type: string_textarea
    weight: 7
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: '1'
  field_visible_only_on_clp:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 52
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 53
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
  start_date:
    type: datetime_timestamp_no_default
    weight: 16
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield_with_counter
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
      use_field_maxlength: true
      maxlength: 0
      counter_position: after
      js_prevent_submit: true
      count_only_mode: false
      count_html_characters: true
      textcount_status_message: 'Maxlength: <span class="maxlength_count">@maxlength</span> Used: <span class="current_count">@current_length</span> Remaining: <span class="remaining_count">@remaining_count</span>'
    third_party_settings: {  }
  translation:
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 54
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 55
    region: content
    settings: {  }
    third_party_settings: {  }
  url_redirects:
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  variants: true
