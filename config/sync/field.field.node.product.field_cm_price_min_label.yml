uuid: 5cde6835-e84e-40bb-89c8-ba2eb6bee78a
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cm_price_min_label
    - node.type.product
id: node.product.field_cm_price_min_label
field_name: field_cm_price_min_label
entity_type: node
bundle: product
label: 'Price min label'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
