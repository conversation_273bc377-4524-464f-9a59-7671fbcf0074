uuid: ff9e09d4-2b9d-4e94-ba01-787fbf6ef1ec
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_tv_channel_resolutions
    - node.type.tv_channel
    - taxonomy.vocabulary.tv_channels_resolutions
id: node.tv_channel.field_tv_channel_resolutions
field_name: field_tv_channel_resolutions
entity_type: node
bundle: tv_channel
label: 'Tv channel resolutions'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tv_channels_resolutions: tv_channels_resolutions
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
