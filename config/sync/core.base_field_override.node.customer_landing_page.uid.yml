uuid: *************-4dbb-b74a-fa7cd7da97a5
langcode: fr
status: true
dependencies:
  config:
    - node.type.customer_landing_page
id: node.customer_landing_page.uid
field_name: uid
entity_type: node
bundle: customer_landing_page
label: 'Écrit par'
description: "Le nom d'utilisateur de l'auteur du contenu."
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
