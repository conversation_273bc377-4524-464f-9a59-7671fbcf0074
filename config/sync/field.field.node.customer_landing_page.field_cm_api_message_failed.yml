uuid: e07cb0c7-c5a4-40fb-aed2-a9c326869381
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cm_api_message_failed
    - node.type.customer_landing_page
  module:
    - text
id: node.customer_landing_page.field_cm_api_message_failed
field_name: field_cm_api_message_failed
entity_type: node
bundle: customer_landing_page
label: Failed
description: "<b>Example</b>: can be isplayed to the customer when the change abo failed:\r\n<b><br />Allowed tokens</b>:\r\n<ul>\r\n<li>[wimc-customer-name]</li>\r\n<li>[wimc-customer-firstName]</li>\r\n<li>[wimc-customer-campaignID]</li>\r\n<li>[wimc-customer-campaignEndDt]</li>\r\n</ul>"
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long
