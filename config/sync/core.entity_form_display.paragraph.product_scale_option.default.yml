uuid: 3f4dcc56-0302-43a5-9d7b-60cc9dde4863
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.product_scale_option.field_cm_cta_link
    - field.field.paragraph.product_scale_option.field_cm_cta_link_track
    - field.field.paragraph.product_scale_option.field_cm_highlighted
    - field.field.paragraph.product_scale_option.field_product_scale_option_price
    - field.field.paragraph.product_scale_option.field_product_scale_option_text
    - paragraphs.paragraphs_type.product_scale_option
  module:
    - field_group
    - link_target
third_party_settings:
  field_group:
    group_cta:
      children:
        - field_cm_cta_link
        - field_cm_cta_link_track
      label: CTA
      region: content
      parent_name: ''
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        id: ''
        open: true
        required_fields: true
id: paragraph.product_scale_option.default
targetEntityType: paragraph
bundle: product_scale_option
mode: default
content:
  color_pattern:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_cta_link:
    type: link_target_field_widget
    weight: 4
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
      link_target: ''
    third_party_settings: {  }
  field_cm_cta_link_track:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_highlighted:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_product_scale_option_price:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_product_scale_option_text:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  brands_hidden: true
  compact: true
  created: true
  status: true
  uid: true
