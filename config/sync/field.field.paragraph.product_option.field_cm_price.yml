uuid: 80871ac2-ebcc-4a9f-98c7-6c26cc781ac1
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_price
    - paragraphs.paragraphs_type.product_option
id: paragraph.product_option.field_cm_price
field_name: field_cm_price
entity_type: paragraph
bundle: product_option
label: Price
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: float
