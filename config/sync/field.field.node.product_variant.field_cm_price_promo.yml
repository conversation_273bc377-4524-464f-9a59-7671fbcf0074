uuid: 8084da38-3450-4212-9b59-ba2214f871ba
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_price_promo
    - node.type.product_variant
id: node.product_variant.field_cm_price_promo
field_name: field_cm_price_promo
entity_type: node
bundle: product_variant
label: 'Price Promo Amount'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: float
