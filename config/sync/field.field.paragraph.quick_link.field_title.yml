uuid: a0ac7187-1d97-4a8e-8f12-4e48d8838b76
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_title
    - filter.format.simple
    - paragraphs.paragraphs_type.quick_link
  module:
    - text
id: paragraph.quick_link.field_title
field_name: field_title
entity_type: paragraph
bundle: quick_link
label: Title
description: "The token <i> %highest-promo-percentage%</i> can be used for example in <i>Available Offers</i>.\r\nEx. Abos Mobile, Abos Internet & TV, Up to %highest-promo-percentage% off mobile"
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - simple
field_type: text
