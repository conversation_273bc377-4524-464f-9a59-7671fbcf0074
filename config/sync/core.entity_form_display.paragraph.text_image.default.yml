uuid: abdac439-49fc-4099-93d9-cece3584afa2
langcode: de
status: true
dependencies:
  config:
    - field.field.paragraph.text_image.field_cm_text
    - field.field.paragraph.text_image.field_one_media
    - paragraphs.paragraphs_type.text_image
  module:
    - media_library
    - text
id: paragraph.text_image.default
targetEntityType: paragraph
bundle: text_image
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  color_pattern:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_text:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_one_media:
    type: media_library_widget
    weight: 2
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  translation:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  compact: true
  created: true
  status: true
  title: true
