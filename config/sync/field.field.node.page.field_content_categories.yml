uuid: 647de662-37cb-4091-aa0a-8927fdec146a
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_content_categories
    - node.type.page
    - taxonomy.vocabulary.content_categories
id: node.page.field_content_categories
field_name: field_content_categories
entity_type: node
bundle: page
label: 'Content Categories'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      content_categories: content_categories
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
