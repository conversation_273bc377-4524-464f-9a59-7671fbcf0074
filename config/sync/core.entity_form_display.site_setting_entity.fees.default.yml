uuid: 7035ee98-3543-4a68-b373-88631357878d
langcode: fr
status: true
dependencies:
  config:
    - field.field.site_setting_entity.fees.field_wireline_activation_fee
    - site_settings.site_setting_entity_type.fees
id: site_setting_entity.fees.default
targetEntityType: site_setting_entity
bundle: fees
mode: default
content:
  description:
    type: string_textfield
    weight: 26
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_wireline_activation_fee:
    type: number
    weight: 27
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  group:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 10
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  user_id:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
