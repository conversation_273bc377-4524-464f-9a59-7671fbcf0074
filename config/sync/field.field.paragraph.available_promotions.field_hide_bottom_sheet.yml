uuid: fad95cf1-f268-466f-b440-18e6b721f84d
langcode: de
status: true
dependencies:
  config:
    - field.storage.paragraph.field_hide_bottom_sheet
    - paragraphs.paragraphs_type.available_promotions
id: paragraph.available_promotions.field_hide_bottom_sheet
field_name: field_hide_bottom_sheet
entity_type: paragraph
bundle: available_promotions
label: 'Hide Bottom Sheet (Hero Deals)'
description: 'Enable this field to hide the bottom sheet on each card for Hero Deals.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  on_label: An
  off_label: Aus
field_type: boolean
