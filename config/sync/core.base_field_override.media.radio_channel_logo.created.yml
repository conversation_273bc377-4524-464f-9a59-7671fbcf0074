uuid: ff331121-f254-4b98-b348-6f15a25cacff
langcode: en
status: true
dependencies:
  config:
    - media.type.radio_channel_logo
id: media.radio_channel_logo.created
field_name: created
entity_type: media
bundle: radio_channel_logo
label: 'Authored on'
description: 'The time the media item was created.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getRequestTime'
settings: {  }
field_type: created
