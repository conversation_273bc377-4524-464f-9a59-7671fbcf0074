uuid: 43c8818a-5e7e-43e5-9a2d-ec8f1bf04e6d
langcode: fr
status: true
dependencies:
  config:
    - field.storage.media.field_media_description
    - field.storage.media.field_media_file
    - field.storage.node.field_cm_content
    - field.storage.node.field_cm_lead
    - field.storage.node.field_cm_text
    - field.storage.node.field_marketing_sentence
    - field.storage.node.field_smartphone_base
    - field.storage.node.field_smartphone_brand
    - field.storage.node.field_smartphone_seo_name
    - field.storage.paragraph.field_cm_headlines
    - field.storage.paragraph.field_cm_text
    - field.storage.paragraph.field_content_categories
    - field.storage.paragraph.field_devices_customer_text
    - field.storage.paragraph.field_devices_customer_title
    - field.storage.paragraph.field_tabs_items
    - field.storage.paragraph.field_title
    - field.storage.taxonomy_term.field_cm_subtitle
    - search_api.server.wingo
  module:
    - file
    - media
    - node
    - paragraphs
    - search_api_exclude
    - taxonomy
    - wimc_admin
    - wimc_paragraphs
id: wingo
name: Wingo
description: ''
read_only: false
field_settings:
  cm_meta_description:
    label: 'Meta Description'
    datasource_id: 'entity:node'
    property_path: cm_meta_description
    type: text
    dependencies:
      module:
        - wimc_admin
  field_cm_lead:
    label: Lead
    datasource_id: 'entity:node'
    property_path: field_cm_lead
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_lead
  field_cm_subtitle:
    label: 'Content » Paragraph » Headlines » Taxonomy term » Subtitle'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_cm_headlines:entity:field_cm_subtitle'
    type: string
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_cm_headlines
        - field.storage.taxonomy_term.field_cm_subtitle
      module:
        - paragraphs
        - taxonomy
  field_cm_text:
    label: 'Content » Paragraphe » Tabs » Paragraphe » Text'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_tabs_items:entity:field_cm_text'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_cm_text
        - field.storage.paragraph.field_tabs_items
      module:
        - paragraphs
  field_cm_text_1:
    label: Answer
    datasource_id: 'entity:node'
    property_path: field_cm_text
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_text
  field_cm_text_2:
    label: 'Content » Paragraphe » Text'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_cm_text'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_cm_text
      module:
        - paragraphs
  field_content_categories:
    label: 'Content » Paragraph » Content Categories'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_content_categories'
    type: integer
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_content_categories
      module:
        - paragraphs
  field_devices_customer_text_1:
    label: 'Content » Paragraphe » Texte'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_devices_customer_text'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_devices_customer_text
      module:
        - paragraphs
  field_devices_customer_title_1:
    label: 'Content » Paragraphe » Title'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_devices_customer_title'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_devices_customer_title
      module:
        - paragraphs
  field_marketing_sentence:
    label: 'Marketing Sentence'
    datasource_id: 'entity:node'
    property_path: field_marketing_sentence
    type: text
    dependencies:
      config:
        - field.storage.node.field_marketing_sentence
  field_media_description:
    label: Description
    datasource_id: 'entity:media'
    property_path: field_media_description
    type: text
    dependencies:
      config:
        - field.storage.media.field_media_description
  field_smartphone_brand:
    label: 'Smartphone Base » Contenu » Brand'
    datasource_id: 'entity:node'
    property_path: 'field_smartphone_base:entity:field_smartphone_brand'
    type: text
    dependencies:
      config:
        - field.storage.node.field_smartphone_base
        - field.storage.node.field_smartphone_brand
      module:
        - node
  field_smartphone_seo_name:
    label: 'Seo Name'
    datasource_id: 'entity:node'
    property_path: field_smartphone_seo_name
    type: string
    dependencies:
      config:
        - field.storage.node.field_smartphone_seo_name
  field_title:
    label: 'Content » Paragraphe » Tabs » Paragraphe » Title'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_tabs_items:entity:field_title'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_tabs_items
        - field.storage.paragraph.field_title
      module:
        - paragraphs
  field_title_1:
    label: 'Content » Paragraphe » Title'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_title'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_title
      module:
        - paragraphs
  filemime:
    label: 'Datei » Fichier » Type MIME du fichier'
    datasource_id: 'entity:media'
    property_path: 'field_media_file:entity:filemime'
    type: string
    dependencies:
      config:
        - field.storage.media.field_media_file
      module:
        - file
  filename:
    label: 'Datei » Fichier » Nom du fichier'
    datasource_id: 'entity:media'
    property_path: 'field_media_file:entity:filename'
    type: string
    dependencies:
      config:
        - field.storage.media.field_media_file
      module:
        - file
  name:
    label: 'Content » Paragraph » Headlines » Taxonomy term » Name'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_cm_headlines:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_cm_headlines
      module:
        - paragraphs
        - taxonomy
  name_2:
    label: Nom
    datasource_id: 'entity:media'
    property_path: name
    type: text
    dependencies:
      module:
        - media
  node_grants:
    label: "Information d'accès du nœud."
    property_path: search_api_node_grants
    type: string
    indexed_locked: true
    type_locked: true
    hidden: true
  status:
    label: status
    datasource_id: 'entity:node'
    property_path: status
    type: boolean
    indexed_locked: true
    type_locked: true
    dependencies:
      module:
        - node
  title:
    label: Title
    datasource_id: 'entity:node'
    property_path: title
    type: text
    boost: 5.0
    dependencies:
      module:
        - node
  title_1:
    label: 'Content » Paragraphe » Tabs » Paragraphe » Title'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:field_tabs_items:entity:title'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
        - field.storage.paragraph.field_tabs_items
      module:
        - paragraphs
        - wimc_paragraphs
  title_2:
    label: 'Content » Paragraphe » Title'
    datasource_id: 'entity:node'
    property_path: 'field_cm_content:entity:title'
    type: text
    dependencies:
      config:
        - field.storage.node.field_cm_content
      module:
        - paragraphs
        - wimc_paragraphs
  uid:
    label: uid
    datasource_id: 'entity:node'
    property_path: uid
    type: integer
    indexed_locked: true
    type_locked: true
    dependencies:
      module:
        - node
  url:
    label: 'Datei » Fichier » URI » Root-relative file URL'
    datasource_id: 'entity:media'
    property_path: 'field_media_file:entity:uri:url'
    type: string
    dependencies:
      config:
        - field.storage.media.field_media_file
      module:
        - file
datasource_settings:
  'entity:media':
    bundles:
      default: false
      selected:
        - download
    languages:
      default: true
      selected: {  }
  'entity:node':
    bundles:
      default: false
      selected:
        - article
        - faq
        - page
        - product
        - product_option
        - smartphone_variant
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  content_access:
    weights:
      preprocess_query: -30
  custom_value: {  }
  entity_type: {  }
  highlight:
    weights:
      postprocess_query: 0
    prefix: '<strong>'
    suffix: '</strong>'
    excerpt: true
    excerpt_always: false
    excerpt_length: 256
    exclude_fields: {  }
    highlight: always
    highlight_partial: true
  ignorecase:
    weights:
      preprocess_index: -20
      preprocess_query: -20
    all_fields: true
    fields:
      - cm_meta_description
      - field_cm_lead
      - field_cm_subtitle
      - field_cm_text
      - field_cm_text_1
      - field_cm_text_2
      - field_devices_customer_text_1
      - field_devices_customer_title_1
      - field_marketing_sentence
      - field_media_description
      - field_smartphone_brand
      - field_smartphone_seo_name
      - field_title
      - field_title_1
      - filemime
      - filename
      - name
      - name_2
      - title
      - title_1
      - title_2
      - url
  language_with_fallback: {  }
  node_exclude: {  }
  rendered_item: {  }
  tokenizer:
    weights:
      preprocess_index: -6
      preprocess_query: -6
    all_fields: false
    fields:
      - title
    spaces: ''
    ignored: ._-
    overlap_cjk: 1
    minimum_word_size: '2'
  transliteration:
    weights:
      preprocess_index: -20
      preprocess_query: -20
    all_fields: true
    fields:
      - cm_meta_description
      - field_cm_lead
      - field_cm_subtitle
      - field_cm_text
      - field_cm_text_1
      - field_cm_text_2
      - field_devices_customer_text_1
      - field_devices_customer_title_1
      - field_marketing_sentence
      - field_media_description
      - field_smartphone_brand
      - field_smartphone_seo_name
      - field_title
      - field_title_1
      - filemime
      - filename
      - name
      - name_2
      - title
      - title_1
      - title_2
      - url
  type_boost:
    weights:
      preprocess_index: 0
    boosts:
      'entity:media':
        datasource_boost: 1.0
      'entity:node':
        datasource_boost: 1.0
        bundle_boosts:
          product: 5.0
          smartphone_variant: 3.0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  index_directly: true
  track_changes_in_references: true
server: wingo
