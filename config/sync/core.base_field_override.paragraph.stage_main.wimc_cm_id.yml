uuid: 3c7d64f3-5cc9-42b4-8e89-e23f04b357ba
langcode: en
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.stage_main
id: paragraph.stage_main.wimc_cm_id
field_name: wimc_cm_id
entity_type: paragraph
bundle: stage_main
label: 'Paragraph Type ID'
description: 'Generic ID of the paragraph type. If it is set, frontend application can use it to generate anchor link to the particular section in the rendered page. In the backend side, you can use this value in a link field. Example: <strong>#paragraph-id</strong>'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
