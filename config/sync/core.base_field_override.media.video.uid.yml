uuid: 31babff4-5c69-4854-84ba-cc201e7bfe9d
langcode: fr
status: true
dependencies:
  config:
    - media.type.video
id: media.video.uid
field_name: uid
entity_type: media
bundle: video
label: 'Écrit par'
description: "L'identifiant (ID) de l'auteur."
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
