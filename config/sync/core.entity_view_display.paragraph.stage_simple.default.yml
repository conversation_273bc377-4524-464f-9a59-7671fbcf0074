uuid: 5f17a6ab-2e01-4d2a-b0e1-325691f1b4fd
langcode: de
status: true
dependencies:
  config:
    - field.field.paragraph.stage_simple.field_cm_lead
    - field.field.paragraph.stage_simple.field_cm_media
    - field.field.paragraph.stage_simple.field_cm_media_mobile
    - field.field.paragraph.stage_simple.field_cm_related_bg_color
    - field.field.paragraph.stage_simple.field_cm_render_component
    - field.field.paragraph.stage_simple.field_int_value_with_stepper
    - field.field.paragraph.stage_simple.field_intro_product
    - field.field.paragraph.stage_simple.field_title
    - paragraphs.paragraphs_type.stage_simple
  module:
    - text
id: paragraph.stage_simple.default
targetEntityType: paragraph
bundle: stage_simple
mode: default
content:
  field_cm_lead:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_cm_media: true
  field_cm_media_mobile: true
  field_cm_related_bg_color: true
  field_cm_render_component: true
  field_int_value_with_stepper: true
  field_intro_product: true
  field_title: true
  search_api_excerpt: true
