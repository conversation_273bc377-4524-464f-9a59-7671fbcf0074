uuid: 8ff70521-901b-434a-a0e0-a50d8ebfbc88
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_cta_link
    - paragraphs.paragraphs_type.available_products
  module:
    - link
    - link_field_autocomplete_filter
third_party_settings:
  link_field_autocomplete_filter:
    negate: false
    allowed_content_types:
      page: page
      product: product
      customer_landing_page: '0'
      eligibility_check: '0'
      faq: '0'
      option: '0'
      prepaid: '0'
      product_option: '0'
      product_variant: '0'
      radio_channel: '0'
      smartphone: '0'
      tv_channel: '0'
id: paragraph.available_products.field_cm_cta_link
field_name: field_cm_cta_link
entity_type: paragraph
bundle: available_products
label: CTA
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  title: 2
  link_type: 17
field_type: link
