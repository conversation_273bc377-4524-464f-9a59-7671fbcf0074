uuid: 20a78c44-0a7b-4fff-a2d9-5483abb29bfe
langcode: fr
status: true
dependencies:
  config:
    - media.type.video
id: media.video.created
field_name: created
entity_type: media
bundle: video
label: 'Écrit le'
description: 'Date à laquelle cet élément de média a été créé.'
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getRequestTime'
settings: {  }
field_type: created
