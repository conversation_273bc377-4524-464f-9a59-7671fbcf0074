uuid: 32425eb4-684b-491f-b25a-38d781871f6d
langcode: en
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.hotline
id: paragraph.hotline.wimc_cm_id
field_name: wimc_cm_id
entity_type: paragraph
bundle: hotline
label: 'Paragraph Type ID'
description: 'Generic ID of the paragraph type. If it is set, frontend application can use it to generate anchor link to the particular section in the rendered page. In the backend side, you can use this value in a link field. Example: <strong>#paragraph-id</strong>'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
