uuid: 19bb8c85-8b36-4e9c-adb4-b3a036ed7263
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_mobile_region
    - paragraphs.paragraphs_type.subscription_detail_item
    - taxonomy.vocabulary.mobile_region
id: paragraph.subscription_detail_item.field_cm_mobile_region
field_name: field_cm_mobile_region
entity_type: paragraph
bundle: subscription_detail_item
label: 'Mobile Region'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      mobile_region: mobile_region
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
