uuid: 33d9159a-65cd-4fc8-b1c0-6fecc3ca6407
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_border_color
    - node.type.product_bundle
    - taxonomy.vocabulary.colors
id: node.product_bundle.field_border_color
field_name: field_border_color
entity_type: node
bundle: product_bundle
label: 'Border Color'
description: 'If provided, a border with this color will be drawn around the teaser card.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      colors: colors
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
