uuid: a9dd52bc-b66e-4dce-92b1-cdd4553b8d09
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.media.media_library
    - field.field.media.street_credit.field_cm_brands_hidden
    - field.field.media.street_credit.field_cm_media_cms_name
    - field.field.media.street_credit.field_cm_media_description
    - field.field.media.street_credit.field_cm_media_text
    - field.field.media.street_credit.field_media_image
    - image.style.thumbnail
    - media.type.street_credit
  module:
    - svg_image
id: media.street_credit.media_library
targetEntityType: media
bundle: street_credit
mode: media_library
content:
  field_media_image:
    type: image_image
    weight: 5
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  field_cm_brands_hidden: true
  field_cm_media_cms_name: true
  field_cm_media_description: true
  field_cm_media_text: true
  langcode: true
  path: true
  publish_on: true
  publish_state: true
  scheduler_settings: true
  status: true
  translation: true
  uid: true
  unpublish_on: true
  unpublish_state: true
