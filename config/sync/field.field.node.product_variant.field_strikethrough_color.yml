uuid: e2edffde-b1d1-4871-bb1b-b1012bb05de2
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_strikethrough_color
    - node.type.product_variant
    - taxonomy.vocabulary.colors
id: node.product_variant.field_strikethrough_color
field_name: field_strikethrough_color
entity_type: node
bundle: product_variant
label: 'Strikethrough Color'
description: 'If no color is selected, black will be used.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      colors: colors
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
