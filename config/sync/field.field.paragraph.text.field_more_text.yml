uuid: 4e43fd26-0725-4565-9b5a-caf19eac04ce
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_more_text
    - filter.format.editorial
    - paragraphs.paragraphs_type.text
  module:
    - text
id: paragraph.text.field_more_text
field_name: field_more_text
entity_type: paragraph
bundle: text
label: 'More Text'
description: 'If provided, the shore more link will be displayed and the full text is shown when the user clicks on the click.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - editorial
field_type: text_long
