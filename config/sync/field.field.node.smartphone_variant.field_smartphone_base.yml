uuid: 99f92857-7a2e-49b0-ac2e-d71908edb4fe
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_smartphone_base
    - node.type.smartphone_base
    - node.type.smartphone_variant
id: node.smartphone_variant.field_smartphone_base
field_name: field_smartphone_base
entity_type: node
bundle: smartphone_variant
label: 'Smartphone Base'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      smartphone_base: smartphone_base
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
