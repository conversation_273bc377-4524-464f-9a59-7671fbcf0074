uuid: 57e625cc-9336-418d-a501-320ec91d5d00
langcode: de
status: true
dependencies:
  config:
    - filter.format.editorial
    - filter.format.simple
    - filter.format.webform_default
    - rest.resource.aliases
    - rest.resource.authentication
    - rest.resource.contact
    - rest.resource.customer_landing_pages
    - rest.resource.eligibility_check
    - rest.resource.faqs
    - rest.resource.faqs_categories
    - rest.resource.faqs_list
    - rest.resource.faqs_revision
    - rest.resource.faqs_revisions
    - rest.resource.faqs_teaser
    - rest.resource.footer
    - rest.resource.layout
    - rest.resource.menus
    - rest.resource.options
    - rest.resource.options_list
    - rest.resource.options_revision
    - rest.resource.options_revisions
    - rest.resource.options_teaser
    - rest.resource.pages
    - rest.resource.pages_revision
    - rest.resource.pages_revisions
    - rest.resource.products
    - rest.resource.products_list
    - rest.resource.products_revision
    - rest.resource.products_revisions
    - rest.resource.routes
    - rest.resource.search_v2
    - rest.resource.settings
    - rest.resource.smartphones
    - rest.resource.tv_channels
    - rest.resource.tv_channels_list
    - search_api_autocomplete.search.search
  module:
    - file
    - filter
    - media
    - mimemail
    - rest
    - search_api_autocomplete
    - site_settings
    - system
_core:
  default_config_hash: dJ0L2DNSj5q6XVZAGsuVDpJTh5UeYkIPwKrUOOpr8YI
id: authenticated
label: 'Angemeldeter Benutzer'
weight: 1
is_admin: false
permissions:
  - 'access content'
  - 'delete own files'
  - 'restful get aliases'
  - 'restful get authentication'
  - 'restful get customer_landing_pages'
  - 'restful get eligibility_check'
  - 'restful get faqs'
  - 'restful get faqs_categories'
  - 'restful get faqs_list'
  - 'restful get faqs_revision'
  - 'restful get faqs_revisions'
  - 'restful get faqs_teaser'
  - 'restful get footer'
  - 'restful get layout'
  - 'restful get menus'
  - 'restful get options'
  - 'restful get options_list'
  - 'restful get options_revision'
  - 'restful get options_revisions'
  - 'restful get options_teaser'
  - 'restful get pages'
  - 'restful get pages_revision'
  - 'restful get pages_revisions'
  - 'restful get products'
  - 'restful get products_list'
  - 'restful get products_revision'
  - 'restful get products_revisions'
  - 'restful get routes'
  - 'restful get search_v2'
  - 'restful get settings'
  - 'restful get smartphones'
  - 'restful get tv_channels'
  - 'restful get tv_channels_list'
  - 'restful post contact'
  - 'restful post customer_landing_pages'
  - 'send arbitrary files'
  - 'use search_api_autocomplete for search'
  - 'use text format editorial'
  - 'use text format simple'
  - 'use text format webform_default'
  - 'view media'
  - 'view published site setting entities'
