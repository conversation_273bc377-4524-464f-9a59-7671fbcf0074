uuid: 59fefafb-514f-40a2-8304-d6fd330da610
langcode: de
status: true
dependencies:
  module:
    - node
_core:
  default_config_hash: 2UI87N8fS5nAxF3CmazEEYkYE2aqeGXWq4hinZOxv8c
id: smartphone_variant
label: 'Smartphone Variant'
type: 'canonical_entities:node'
pattern: 'smartphones/[node:title]-[node:field_smartphone_material_number]'
selection_criteria:
  4bb00c68-d2b0-4b5e-a900-7068d78a1d43:
    id: 'entity_bundle:node'
    negate: false
    uuid: 4bb00c68-d2b0-4b5e-a900-7068d78a1d43
    context_mapping:
      node: node
    bundles:
      smartphone_variant: smartphone_variant
selection_logic: and
weight: -5
relationships: {  }
