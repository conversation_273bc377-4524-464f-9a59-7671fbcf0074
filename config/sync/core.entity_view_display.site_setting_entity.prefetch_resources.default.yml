uuid: 858be638-a2ad-447c-99e7-3d6b80dcc46c
langcode: de
status: true
dependencies:
  config:
    - field.field.site_setting_entity.prefetch_resources.field_external_links
    - site_settings.site_setting_entity_type.prefetch_resources
  module:
    - link
    - user
id: site_setting_entity.prefetch_resources.default
targetEntityType: site_setting_entity
bundle: prefetch_resources
mode: default
content:
  description:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_external_links:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 2
    region: content
  group:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  user_id:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  langcode: true
  search_api_excerpt: true
