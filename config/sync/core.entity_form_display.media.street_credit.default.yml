uuid: 888912c9-1b7d-4e9f-9604-2e3a6788be4d
langcode: en
status: true
dependencies:
  config:
    - field.field.media.street_credit.field_cm_brands_hidden
    - field.field.media.street_credit.field_cm_media_cms_name
    - field.field.media.street_credit.field_cm_media_description
    - field.field.media.street_credit.field_cm_media_text
    - field.field.media.street_credit.field_media_image
    - image.style.thumbnail
    - media.type.street_credit
  module:
    - path
    - svg_image
    - text
id: media.street_credit.default
targetEntityType: media
bundle: street_credit
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_brands_hidden:
    type: options_buttons
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_media_cms_name:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_media_description:
    type: text_textarea
    weight: 6
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_cm_media_text:
    type: text_textarea
    weight: 5
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_media_image:
    type: image_image
    weight: 4
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 0
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 11
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 7
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true
