uuid: 93959425-b350-4e26-9a31-d8c25c0f2f0d
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.quick_links.field_cm_content
    - field.field.paragraph.quick_links.field_cm_related_bg_color
    - paragraphs.paragraphs_type.quick_links
  module:
    - field_group
    - paragraphs
third_party_settings:
  field_group:
    group_content:
      children:
        - field_cm_content
        - wimc_cm_id
        - status
      label: Content
      region: content
      parent_name: ''
      weight: 0
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_config:
      children:
        - field_cm_related_bg_color
      label: Config
      region: content
      parent_name: ''
      weight: 1
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: paragraph.quick_links.default
targetEntityType: paragraph
bundle: quick_links
mode: default
content:
  field_cm_content:
    type: paragraphs
    weight: 1
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: editorial
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cm_related_bg_color:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  brands_hidden: true
  color_pattern: true
  compact: true
  created: true
  title: true
