uuid: b80bb2f6-80d1-437d-9a23-f3456b6cc8b5
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_media
    - media.type.image
    - media.type.svg
    - node.type.product_option
id: node.product_option.field_cm_media
field_name: field_cm_media
entity_type: node
bundle: product_option
label: Media
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
      svg: svg
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: image
field_type: entity_reference
