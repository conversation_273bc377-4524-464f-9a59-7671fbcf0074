uuid: 6557ced4-1a01-40ea-8b71-70ddf2849368
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_price_promo
    - node.type.product_bundle
id: node.product_bundle.field_cm_price_promo
field_name: field_cm_price_promo
entity_type: node
bundle: product_bundle
label: 'Price Promo Amount'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: float
