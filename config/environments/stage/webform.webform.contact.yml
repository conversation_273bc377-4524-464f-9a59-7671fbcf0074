uuid: 1f65e83c-f587-4ea3-aafd-f6659dd6510f
langcode: de
status: open
dependencies:
  module:
    - honeypot
  enforced:
    module:
      - webform
third_party_settings:
  honeypot:
    honeypot: true
    time_restriction: true
_core:
  default_config_hash: CJr5U2B4if_Ff1SZfnNp_csPKwXIynmbUlBC-SmSOAE
weight: 0
open: null
close: null
uid: null
template: false
archive: false
id: contact
title: 'Kontakt Formular'
description: '<p>Basic email contact webform.</p>'
categories: {  }
elements: |-
  request:
    '#type': radios
    '#title': 'Meine Anfrage betrifft'
    '#options':
      mobile: 'Mein Mobile Abo'
      internet: 'Mein Internet Abo'
    '#required': true
    '#default_value': mobile
  topic:
    '#type': select
    '#title': 'Zum Thema'
    '#options':
      activation: Aktivierung
      promotion: Promotion
      technical_issue: 'Technische Störung'
      invoicing: Rechnung
      sim: SIM-Karte
      unfair_advertisements: 'Unlautere Werbung'
      other: Anderes
    '#required': true
    '#required_error': 'Zum Thema ist ein Pflichtfeld'
  first_name:
    '#type': textfield
    '#title': Vorname
    '#required': true
    '#required_error': 'Vorname ist ein Pflichtfeld'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
  last_name:
    '#type': textfield
    '#title': Name
    '#required': true
    '#required_error': 'Name ist ein Pflichtfeld'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
  phone:
    '#type': tel
    '#title': Telefon
    '#required': true
    '#required_error': 'Telefon ist ein Pflichtfeld'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
    '#international': true
    '#international_initial_country': CH
  email:
    '#type': email
    '#title': E-mail
    '#required': true
    '#required_error': 'E-Mail muss eine gültige E-Mail-Adresse sein'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
    '#format': raw
  comment:
    '#type': textarea
    '#title': 'Dein Anliegen'
    '#required': true
    '#required_error': 'Dein Anliegen ist ein Pflichtfeld'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
  customer_id:
    '#type': textfield
    '#title': 'Kundennummer (optional)'
    '#multiple__no_items_message': '<p>No items entered. Please add items below.</p>'
  attachments:
    '#type': managed_file
    '#title': 'Anhang (pdf, png, jpg, jpeg, gif)'
    '#access_view_roles': {  }
    '#file_help': help
    '#max_filesize': '10'
    '#file_extensions': 'pdf, png, jpg, jpeg, gif'
    '#sanitize': true
  captcha:
    '#type': captcha
    '#captcha_type': recaptcha_v3/anonymous
  actions:
    '#type': webform_actions
    '#title': 'Submit button(s)'
    '#submit__label': 'Los geht’s'
css: ''
javascript: ''
settings:
  ajax: true
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: false
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_open_message: ''
  form_close_message: ''
  form_exception_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_autofocus: false
  form_details_toggle: false
  form_reset: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_attributes: {  }
  form_method: ''
  form_action: ''
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_exception_message: ''
  submission_locked_message: ''
  submission_log: false
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: inline
  confirmation_url: '<front>'
  confirmation_title: ''
  confirmation_message: "<p><strong>Vielen Dank für deine Kontaktaufnahme!</strong></p>\r\n\r\n<p>Wir werden uns in Kürze mit dir in Verbindung setzen.</p>"
  confirmation_attributes: {  }
  confirmation_back: false
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  email_mobile:
    id: email
    handler_id: email_mobile
    label: 'Email Mobile'
    notes: ''
    status: true
    conditions:
      enabled:
        ':input[name="request"]':
          value: mobile
    weight: 2
    settings:
      states:
        - completed
      to_mail: '<EMAIL>,<EMAIL>'
      to_options: {  }
      bcc_mail: ''
      bcc_options: {  }
      cc_mail: ''
      cc_options: {  }
      from_mail: '[webform_submission:values:email:raw]'
      from_options: {  }
      from_name: '[webform_submission:values:last_name] [webform_submission:values:first_name]'
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
      subject: '[[webform_submission:values:request] - [webform_submission:values:topic]] Contact Form'
      body: "{{ webform_token('[webform_submission:values]', webform_submission, [], options) }}"
      excluded_elements:
        captcha: captcha
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: true
      html: true
      attachments: true
      twig: true
      theme_name: ''
      parameters: {  }
      debug: false
  email_internet:
    id: email
    handler_id: email_internet
    label: 'Email Internet'
    notes: ''
    status: true
    conditions:
      enabled:
        ':input[name="request"]':
          value: internet
    weight: 1
    settings:
      states:
        - completed
      to_mail: '<EMAIL>,<EMAIL>'
      to_options: {  }
      bcc_mail: <EMAIL>
      bcc_options: {  }
      cc_mail: ''
      cc_options: {  }
      from_mail: '[webform_submission:values:email:raw]'
      from_options: {  }
      from_name: '[webform_submission:values:last_name] [webform_submission:values:first_name]'
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
      subject: '[[webform_submission:values:request] - [webform_submission:values:topic]] Contact Form'
      body: "{{ webform_token('[webform_submission:values]', webform_submission, [], options) }}"
      excluded_elements:
        captcha: captcha
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: true
      html: true
      attachments: true
      twig: true
      theme_name: ''
      parameters: {  }
      debug: false
variants: {  }
