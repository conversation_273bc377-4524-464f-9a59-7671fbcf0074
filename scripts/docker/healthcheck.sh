#!/bin/bash

set -eu

# The  healthcheck.sh  script is a simple script that checks if <PERSON><PERSON><PERSON> is running and healthy.
# It uses Drush to check the status of <PERSON><PERSON><PERSON>. If the status contains the string "Drupal bootstrap" and "Successful" and "Connected",
## it means that <PERSON><PERSON><PERSON> is running and healthy.
# The script also checks the status of PHP-FPM by sending a request to the PHP-FPM status page (http://
## If the status contains the string "start time", it means that PHP-FPM is running and healthy.
# The  healthcheck.sh  script is used by the Dockerfile to define the health check for the Drupal container.
# @note: drush maint:status returns 3 if the site is in maintenance mode and 0 if the site is online.

# Check if the PHP-FPM service is running
export BACKEND_PORT="${BACKEND_PORT:-9000}"
export SCRIPT_NAME=/health/php
export SCRIPT_FILENAME=/health/php
export REQUEST_METHOD=GET

cd /opt/drupal
FPM_STATUS=$(cgi-fcgi -bind -connect 127.0.0.1:${BACKEND_PORT})
DRUPAL_STATUS=$(drush status)

if [[ "$FPM_STATUS" =~ 'start time' ]] && [[ "$DRUPAL_STATUS" == *"Drupal bootstrap"* ]] && [[ "$DRUPAL_STATUS" == *"Successful"* ]] && [[ "$DRUPAL_STATUS" == *"Connected"* ]] && drush maint:status; then
  exit 0
else
  exit 1
fi



